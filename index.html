<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <title>Kamikaze AI</title>
    <meta name="description" content="Advanced AI-powered cryptocurrency trading dashboard with automated bots, real-time analytics, and risk management tools." />
    <meta name="author" content="Kamikaze AI" />

    <meta property="og:title" content="Kamikaze AI" />
    <meta property="og:description" content="Advanced AI-powered cryptocurrency trading dashboard with automated bots, real-time analytics, and risk management tools." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
  </head>

  <body>
    <script>
      window.addEventListener('error', function (event) {
        const errorContainer = document.createElement('div');
        errorContainer.style.position = 'fixed';
        errorContainer.style.top = '0';
        errorContainer.style.left = '0';
        errorContainer.style.width = '100%';
        errorContainer.style.padding = '20px';
        errorContainer.style.backgroundColor = 'red';
        errorContainer.style.color = 'white';
        errorContainer.style.zIndex = '9999';
        errorContainer.innerHTML = `
          <h2>Unhandled Error</h2>
          <p><strong>Message:</strong> ${event.message}</p>
          <p><strong>File:</strong> ${event.filename}</p>
          <p><strong>Line:</strong> ${event.lineno}</p>
          <p><strong>Column:</strong> ${event.colno}</p>
          <pre>${event.error ? event.error.stack : 'No stack available'}</pre>
        `;
        document.body.appendChild(errorContainer);
      });
    </script>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>