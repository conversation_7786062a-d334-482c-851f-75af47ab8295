{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm ci", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "env": {"VITE_APP_ENV": "production"}, "build": {"env": {"VITE_APP_ENV": "production"}}, "regions": ["iad1"], "github": {"enabled": true, "autoAlias": true}}