// Kamikaze Trading Pro - Landing Page JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeAnimations();
    initializeCharts();
    initializeScrollEffects();
    initializeMobileMenu();
});

// Navigation Functions
function initializeNavigation() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Active navigation highlighting
    window.addEventListener('scroll', updateActiveNavigation);
}

function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// Mobile Menu Functions
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.getElementById('navMenu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            this.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                mobileMenuToggle.classList.remove('active');
            });
        });
    }
}

// Enhanced Animation Functions
function initializeAnimations() {
    // Animate elements on scroll with staggered timing
    const observerOptions = {
        threshold: 0.15,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add staggered delay for multiple elements
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 150);
            }
        });
    }, observerOptions);

    // Observe elements for animation with different categories
    const animateElements = document.querySelectorAll('.feature-card, .analytics-card, .pricing-card, .stat-item');
    animateElements.forEach(element => {
        observer.observe(element);
    });

    // Initialize floating animations
    initializeFloatingElements();

    // Initialize particle effects
    initializeParticleEffects();

    // Add CSS for animations
    addAnimationStyles();
}

function initializeFloatingElements() {
    const floatingElements = document.querySelectorAll('.floating-element');
    floatingElements.forEach((element, index) => {
        // Add random delays and durations for more natural movement
        const delay = Math.random() * 2;
        const duration = 6 + Math.random() * 4;
        element.style.animationDelay = `${delay}s`;
        element.style.animationDuration = `${duration}s`;
    });
}

function initializeParticleEffects() {
    // Create subtle particle effects for hero section
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        createParticles(heroSection, 15);
    }
}

function createParticles(container, count) {
    for (let i = 0; i < count; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: hsl(217, 91%, 60%, 0.3);
            border-radius: 50%;
            pointer-events: none;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: particleFloat ${8 + Math.random() * 4}s infinite linear;
            animation-delay: ${Math.random() * 8}s;
        `;
        container.appendChild(particle);
    }
}

function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .feature-card, .analytics-card, .pricing-card, .stat-item {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .feature-card.animate-in, .analytics-card.animate-in, .pricing-card.animate-in,
        .stat-item.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .nav-menu.active {
            display: flex !important;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: hsl(var(--background), 0.95);
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1rem;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px hsl(0, 0%, 0%, 0.3);
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        .particle {
            z-index: 1;
        }

        @media (min-width: 769px) {
            .nav-menu.active {
                display: flex !important;
                position: static;
                flex-direction: row;
                background: none;
                border: none;
                padding: 0;
                margin: 0;
                box-shadow: none;
                animation: none;
            }
        }

        /* Enhanced hover effects */
        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .dashboard-mockup:hover .chart-point {
            animation: pulse 1s infinite;
        }

        .btn-hero-primary:hover, .btn-hero-secondary:hover {
            animation: buttonPulse 0.6s ease;
        }

        @keyframes buttonPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Smooth transitions for all interactive elements */
        .feature-card, .analytics-card, .pricing-card, .dashboard-mockup,
        .btn-hero-primary, .btn-hero-secondary, .stat-item {
            will-change: transform;
        }
    `;
    document.head.appendChild(style);
}

// Chart Functions
function initializeCharts() {
    const portfolioChart = document.getElementById('portfolioChart');
    
    if (portfolioChart) {
        drawPortfolioChart(portfolioChart);
    }
    
    // Animate trading chart
    animateTradingChart();
}

function drawPortfolioChart(canvas) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width = canvas.offsetWidth * 2; // Retina display
    const height = canvas.height = canvas.offsetHeight * 2;
    ctx.scale(2, 2);
    
    // Chart data
    const data = [
        { x: 0, y: 100 },
        { x: 50, y: 120 },
        { x: 100, y: 115 },
        { x: 150, y: 140 },
        { x: 200, y: 135 },
        { x: 250, y: 160 },
        { x: 300, y: 155 },
        { x: 350, y: 175 },
        { x: 400, y: 170 }
    ];
    
    // Draw gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, height / 2);
    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)');
    gradient.addColorStop(1, 'rgba(59, 130, 246, 0)');
    
    // Draw area
    ctx.beginPath();
    ctx.moveTo(data[0].x, height / 2);
    data.forEach((point, index) => {
        const x = (point.x / 400) * (width / 2);
        const y = (height / 2) - ((point.y - 100) / 100) * (height / 4);
        
        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });
    ctx.lineTo(width / 2, height / 2);
    ctx.lineTo(data[0].x, height / 2);
    ctx.fillStyle = gradient;
    ctx.fill();
    
    // Draw line
    ctx.beginPath();
    data.forEach((point, index) => {
        const x = (point.x / 400) * (width / 2);
        const y = (height / 2) - ((point.y - 100) / 100) * (height / 4);
        
        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // Draw points
    data.forEach(point => {
        const x = (point.x / 400) * (width / 2);
        const y = (height / 2) - ((point.y - 100) / 100) * (height / 4);
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fillStyle = '#3b82f6';
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
    });
}

function animateTradingChart() {
    const chartLine = document.querySelector('.chart-line');
    if (chartLine) {
        const pathLength = chartLine.getTotalLength();
        chartLine.style.strokeDasharray = pathLength;
        chartLine.style.strokeDashoffset = pathLength;
        
        // Animate the line drawing
        setTimeout(() => {
            chartLine.style.transition = 'stroke-dashoffset 2s ease-in-out';
            chartLine.style.strokeDashoffset = 0;
        }, 500);
    }
}

// Scroll Effects
function initializeScrollEffects() {
    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroVisual = document.querySelector('.hero-visual');
        
        if (heroVisual) {
            const rate = scrolled * -0.5;
            heroVisual.style.transform = `translateY(${rate}px)`;
        }
        
        // Update header background opacity
        const header = document.querySelector('.header-nav');
        if (header) {
            const opacity = Math.min(scrolled / 100, 0.95);
            header.style.background = `hsla(0, 0%, 3%, ${opacity})`;
        }
    });
    
    // Add scroll-to-top functionality
    addScrollToTop();
}

function addScrollToTop() {
    // Create scroll to top button
    const scrollButton = document.createElement('button');
    scrollButton.innerHTML = '↑';
    scrollButton.className = 'scroll-to-top';
    scrollButton.style.cssText = `
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--gradient-primary);
        color: white;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    `;
    
    document.body.appendChild(scrollButton);
    
    // Show/hide scroll button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollButton.style.opacity = '1';
            scrollButton.style.visibility = 'visible';
        } else {
            scrollButton.style.opacity = '0';
            scrollButton.style.visibility = 'hidden';
        }
    });
    
    // Scroll to top functionality
    scrollButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Utility Functions
function openApp() {
    // Redirect to the main application
    window.location.href = '/';
}

// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format the number based on original format
            const originalText = counter.textContent;
            if (originalText.includes('$')) {
                if (originalText.includes('B')) {
                    counter.textContent = `$${(current / 1000).toFixed(1)}B+`;
                } else if (originalText.includes('K')) {
                    counter.textContent = `${Math.floor(current)}K+`;
                } else {
                    counter.textContent = `$${Math.floor(current).toLocaleString()}`;
                }
            } else if (originalText.includes('%')) {
                counter.textContent = `${current.toFixed(1)}%`;
            } else {
                counter.textContent = `${Math.floor(current).toLocaleString()}+`;
            }
        }, 16);
    });
}

// Initialize counter animation when hero section is visible
const heroObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            heroObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

document.addEventListener('DOMContentLoaded', function() {
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroObserver.observe(heroSection);
    }
});

// Performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced scroll handler
const debouncedScrollHandler = debounce(function() {
    updateActiveNavigation();
}, 10);

window.addEventListener('scroll', debouncedScrollHandler);
