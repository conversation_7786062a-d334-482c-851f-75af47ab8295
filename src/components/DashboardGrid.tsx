import React from 'react';
import { MetricCard, ProfitLossCard, PercentageCard } from './dashboard/MetricCard';
import { PremiumChart } from './dashboard/PremiumChart';
import { PortfolioPerformanceChart } from './dashboard/PortfolioPerformanceChart';
import { AssetAllocation } from './dashboard/AssetAllocation';
import { TradingBotCard } from './dashboard/TradingBotCard';
import { FluxTraderBotCard } from './dashboard/FluxTraderBotCard';
import { TradingCycleAnalysisPanel } from './agents/TradingCycleAnalysisPanel';
import { LiveTradeMonitoringCard } from './agents/LiveTradeMonitoringCard';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { useDashboardData, usePortfolioValue, useTradingBots, useAssetAllocation } from '@/hooks/useDashboardData';
import { Skeleton } from './ui/skeleton';
import { Alert, AlertDescription } from './ui/alert';
import { cn } from '@/lib/utils';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Shield,
  Brain,
  Zap,
  Target,
  BarChart3,
  PieChart,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

// Mock data for charts and components (keeping market data for other components)
const mockMarketData = [
  { name: 'BTC', value: 46000 },
  { name: 'ETH', value: 3025 },
  { name: 'BNB', value: 280 },
  { name: 'ADA', value: 0.45 },
  { name: 'DOT', value: 6.2 },
  { name: 'LINK', value: 14.8 }
];

const mockAIInsights = [
  {
    id: '1',
    type: 'opportunity' as const,
    title: 'BTC Breakout Signal',
    description: 'Technical analysis indicates a potential bullish breakout above $46,500 resistance.',
    confidence: 85,
    timestamp: '2 min ago'
  },
  {
    id: '2',
    type: 'warning' as const,
    title: 'High Volatility Alert',
    description: 'ETH showing increased volatility. Consider reducing position size.',
    confidence: 72,
    timestamp: '5 min ago'
  },
  {
    id: '3',
    type: 'info' as const,
    title: 'Market Sentiment',
    description: 'Fear & Greed index at 65 (Greed). Market showing bullish sentiment.',
    confidence: 90,
    timestamp: '10 min ago'
  }
];

const mockRiskMetrics = {
  maxDrawdown: -5.2,
  sharpeRatio: 1.85,
  volatility: 12.5,
  beta: 1.2,
  var95: -850
};

// Mock data removed - now using real API data

export function DashboardGrid() {
  const {
    overview,
    loading,
    error,
    refreshDashboard,
    clearError,
    isStale
  } = useDashboardData();

  const { totalValueUsd, dailyPnl, dailyPnlPercent } = usePortfolioValue();
  const { bots, activeBotCount, totalBots, averageWinRate } = useTradingBots();
  const assetAllocation = useAssetAllocation();

  const handleBotToggle = async (botId: string) => {
    try {
      console.log('Toggle bot:', botId);

      // Find the bot to determine current status
      const bot = bots.find(b => b.id === botId);
      if (!bot) {
        console.error('Bot not found:', botId);
        return;
      }

      // Determine action based on current status
      const isActive = bot.status === 'active';
      const action = isActive ? 'pause' : 'resume';

      // Call the appropriate API endpoint
      const response = await fetch(`/api/v1/bots/${botId}/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });

      if (response.ok) {
        console.log(`Bot ${botId} ${action}d successfully`);
        // Refresh dashboard data to show updated status
        refreshDashboard();
      } else {
        console.error(`Failed to ${action} bot:`, await response.text());
      }
    } catch (error) {
      console.error('Error toggling bot:', error);
    }
  };

  const handleBotSettings = (botId: string) => {
    console.log('Bot settings:', botId);

    // Navigate to bot settings page or open settings modal
    // For now, we'll navigate to a dedicated settings page
    window.location.href = `/dashboard/bots/${botId}/settings`;
  };

  const handleRefresh = () => {
    clearError();
    refreshDashboard();
  };

  // Show error state
  if (error && !loading) {
    return (
      <div className="space-y-4">
        <Alert className="border-destructive/20 bg-destructive/5">
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load dashboard data: {error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="ml-4"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Loading skeleton
  if (loading && !overview) {
    return (
      <div className="space-y-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-4 lg:gap-6">
          <Skeleton className="xl:col-span-8 h-96" />
          <Skeleton className="xl:col-span-4 h-96" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Key Metrics Row - Properly aligned with consistent heights */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <div className="h-full">
          <MetricCard
            title="Portfolio Value"
            value={`$${totalValueUsd.toLocaleString()}`}
            change={dailyPnlPercent}
            trend={dailyPnl > 0 ? 'up' : 'down'}
            icon={<DollarSign className="h-5 w-5" />}
            variant={dailyPnl > 0 ? 'success' : 'destructive'}
            subtitle="24h change"
            className="h-full"
            loading={loading}
          />
        </div>

        <div className="h-full">
          <ProfitLossCard
            title="Daily P&L"
            profit={dailyPnl}
            change={dailyPnlPercent}
            icon={<TrendingUp className="h-5 w-5" />}
            className="h-full"
            loading={loading}
          />
        </div>

        <div className="h-full">
          <MetricCard
            title="Active Bots"
            value={activeBotCount}
            subtitle={`${totalBots} total`}
            icon={<Activity className="h-5 w-5" />}
            variant="default"
            className="h-full"
            loading={loading}
          />
        </div>

        <div className="h-full">
          <MetricCard
            title="Win Rate"
            value={`${Math.round(averageWinRate)}%`}
            trend={averageWinRate > 50 ? 'up' : 'down'}
            icon={<Target className="h-5 w-5" />}
            variant={averageWinRate > 50 ? 'success' : 'warning'}
            subtitle="Average"
            className="h-full"
            loading={loading}
          />
        </div>
      </div>

      {/* Main Content Grid - Improved responsive layout */}
      <div className="grid grid-cols-1 xl:grid-cols-12 gap-4 lg:gap-6">
        {/* Portfolio Performance Chart - spans 8 columns */}
        <div className="xl:col-span-8 col-span-1">
          <div className="h-full">
            <PortfolioPerformanceChart
              height={200}
              className="h-full"
            />
          </div>
        </div>

        {/* Asset Allocation - spans 4 columns (expanded back to original) */}
        <div className="xl:col-span-4 col-span-1">
          <div className="h-full">
            <AssetAllocation
              title="Asset Allocation"
              data={assetAllocation}
              height={200}
              className="h-full"
              showLegend={true}
            />
          </div>
        </div>

        {/* Trading Bots Grid - spans 8 columns */}
        <div className="xl:col-span-8 col-span-1">
          <Card className="glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                Trading Bots
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 h-full">
                {/* FluxTrader AI Agent - Always show first */}
                <div className="h-full">
                  <FluxTraderBotCard className="h-full" />
                </div>

                {loading ? (
                  // Show loading skeletons
                  Array.from({ length: 2 }).map((_, index) => (
                    <div key={index} className="h-full">
                      <Skeleton className="h-full min-h-[200px]" />
                    </div>
                  ))
                ) : bots.length > 0 ? (
                  bots.map((bot) => (
                    <div key={bot.id} className="h-full">
                      <TradingBotCard
                        bot={{
                          id: bot.id,
                          name: bot.name,
                          status: bot.status as 'active' | 'paused' | 'stopped' | 'error',
                          strategy: bot.strategy,
                          profit: bot.profit,
                          profitPercentage: bot.profit_percentage,
                          trades: bot.trades,
                          winRate: bot.win_rate,
                          lastTrade: bot.last_trade,
                          riskLevel: bot.risk_level as 'low' | 'medium' | 'high'
                        }}
                        onToggle={handleBotToggle}
                        onSettings={handleBotSettings}
                        className="h-full"
                      />
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 flex items-center justify-center h-full min-h-[200px]">
                    <div className="text-center text-muted-foreground">
                      <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No other trading bots configured</p>
                      <p className="text-sm">FluxTrader AI Agent is available above</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Insights - spans 4 columns */}
        <div className="xl:col-span-4 col-span-1">
          <Card className="glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                AI Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 space-y-3 overflow-y-auto">
              {loading ? (
                // Show loading skeletons
                Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton key={index} className="h-20" />
                ))
              ) : overview?.ai_insights && overview.ai_insights.length > 0 ? (
                overview.ai_insights.map((insight) => (
                  <div key={insight.id} className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className={cn(
                        'p-2 rounded-lg flex-shrink-0',
                        insight.type === 'opportunity' && 'bg-success/10 text-success',
                        insight.type === 'warning' && 'bg-warning/10 text-warning',
                        insight.type === 'info' && 'bg-primary/10 text-primary'
                      )}>
                        {insight.type === 'opportunity' && <TrendingUp className="h-4 w-4" />}
                        {insight.type === 'warning' && <AlertTriangle className="h-4 w-4" />}
                        {insight.type === 'info' && <Info className="h-4 w-4" />}
                      </div>
                      <div className="flex-1 space-y-1 min-w-0">
                        <h4 className="font-medium text-foreground text-sm">{insight.title}</h4>
                        <p className="text-xs text-muted-foreground leading-relaxed">{insight.description}</p>
                        <div className="flex items-center justify-between pt-1">
                          <Badge variant="outline" className="text-xs px-2 py-0.5">
                            {insight.confidence}% confidence
                          </Badge>
                          <span className="text-xs text-muted-foreground">{insight.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-full min-h-[200px]">
                  <div className="text-center text-muted-foreground">
                    <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No AI insights available</p>
                    <p className="text-sm">AI analysis will appear here when data is available</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Market Overview - spans 8 columns */}
        <div className="xl:col-span-8 col-span-1">
          <div className="h-full">
            <PremiumChart
              title="Market Overview (24h)"
              data={mockMarketData}
              type="line"
              height={300}
              dataKey="value"
              xAxisKey="name"
              className="h-full"
            />
          </div>
        </div>

        {/* Risk Metrics - spans 4 columns */}
        <div className="xl:col-span-4 col-span-1">
          <Card className="glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                <Shield className="h-5 w-5 text-primary" />
                Risk Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg text-center">
                  <p className="text-xs text-muted-foreground mb-1">Max Drawdown</p>
                  <p className="text-lg font-semibold text-destructive">{mockRiskMetrics.maxDrawdown}%</p>
                </div>
                <div className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg text-center">
                  <p className="text-xs text-muted-foreground mb-1">Sharpe Ratio</p>
                  <p className="text-lg font-semibold text-success">{mockRiskMetrics.sharpeRatio}</p>
                </div>
                <div className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg text-center">
                  <p className="text-xs text-muted-foreground mb-1">Volatility</p>
                  <p className="text-lg font-semibold text-warning">{mockRiskMetrics.volatility}%</p>
                </div>
                <div className="glass-card hover:shadow-elevated transition-all duration-300 p-3 border border-card-border/30 rounded-lg text-center">
                  <p className="text-xs text-muted-foreground mb-1">Beta</p>
                  <p className="text-lg font-semibold text-foreground">{mockRiskMetrics.beta}</p>
                </div>
              </div>
              <div className="glass-card hover:shadow-elevated transition-all duration-300 p-4 border border-card-border/30 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground font-medium">VaR (95%)</span>
                  <span className="text-lg font-semibold text-destructive">
                    ${Math.abs(mockRiskMetrics.var95).toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Performers - spans 6 columns */}
        <div className="xl:col-span-6 col-span-1">
          <Card className="glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto">
              <div className="space-y-3">
                {loading ? (
                  // Show loading skeletons
                  Array.from({ length: 5 }).map((_, index) => (
                    <Skeleton key={index} className="h-16" />
                  ))
                ) : overview?.top_assets && overview.top_assets.length > 0 ? (
                  overview.top_assets.map((asset, index) => (
                  <div key={asset.symbol} className="flex items-center justify-between p-3 glass-card hover:shadow-elevated transition-all duration-300 border border-card-border/30 rounded-lg hover:border-primary/20">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <span className="text-xs font-bold text-primary">{index + 1}</span>
                      </div>
                      <div className="min-w-0">
                        <p className="font-semibold text-foreground">{asset.symbol}</p>
                        <p className="text-xs text-muted-foreground truncate">{asset.name}</p>
                      </div>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <p className="font-semibold text-foreground">${asset.price.toLocaleString()}</p>
                      <div className="flex items-center gap-1 justify-end">
                        {asset.change_percent >= 0 ? (
                          <ArrowUpRight className="h-3 w-3 text-success" />
                        ) : (
                          <ArrowDownRight className="h-3 w-3 text-destructive" />
                        )}
                        <span className={cn(
                          'text-xs font-medium',
                          asset.change_percent >= 0 ? 'text-success' : 'text-destructive'
                        )}>
                          {asset.change_percent > 0 ? '+' : ''}{asset.change_percent.toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full min-h-[200px]">
                    <div className="text-center text-muted-foreground">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No market data available</p>
                      <p className="text-sm">Top performers will appear here when data is available</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Trades - spans 6 columns */}
        <div className="xl:col-span-6 col-span-1">
          <Card className="glass-card hover:shadow-elevated transition-all duration-300 h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Recent Trades
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto">
              <div className="space-y-3">
                {loading ? (
                  // Show loading skeletons
                  Array.from({ length: 5 }).map((_, index) => (
                    <Skeleton key={index} className="h-16" />
                  ))
                ) : overview?.recent_trades && overview.recent_trades.length > 0 ? (
                  overview.recent_trades.slice(0, 5).map((trade) => (
                  <div key={trade.id} className="flex items-center justify-between p-3 glass-card hover:shadow-elevated transition-all duration-300 border border-card-border/30 rounded-lg hover:border-primary/20">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <Badge className={cn(
                        'text-xs flex-shrink-0',
                        trade.side === 'BUY' ? 'bg-success/10 text-success border-success/30' : 'bg-destructive/10 text-destructive border-destructive/30'
                      )}>
                        {trade.side}
                      </Badge>
                      <div className="min-w-0">
                        <p className="font-semibold text-foreground">{trade.symbol}</p>
                        <p className="text-xs text-muted-foreground truncate">{trade.quantity} @ ${trade.price}</p>
                      </div>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <div className="flex items-center gap-1 justify-end">
                        {trade.status === 'completed' && <CheckCircle className="h-3 w-3 text-success" />}
                        {trade.status === 'pending' && <Clock className="h-3 w-3 text-warning" />}
                        {trade.status === 'failed' && <AlertTriangle className="h-3 w-3 text-destructive" />}
                        <span className={cn(
                          'text-sm font-medium',
                          trade.pnl >= 0 ? 'text-success' : 'text-destructive'
                        )}>
                          {trade.pnl > 0 ? '+' : ''}${trade.pnl.toFixed(2)}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">{new Date(trade.timestamp * 1000).toLocaleString()}</p>
                    </div>
                  </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full min-h-[200px]">
                    <div className="text-center text-muted-foreground">
                      <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No recent trades</p>
                      <p className="text-sm">Your trading activity will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FluxTrader Real-time Analysis - spans full width */}
        <div className="xl:col-span-12 col-span-1">
          <TradingCycleAnalysisPanel
            cycleData={null} // This will be populated when connected to a FluxTrader agent
            className="w-full"
          />
        </div>

        {/* FluxTrader Live Trade Monitoring - spans full width */}
        <div className="xl:col-span-12 col-span-1">
          <LiveTradeMonitoringCard
            trades={[]} // This will be populated when connected to a FluxTrader agent
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}