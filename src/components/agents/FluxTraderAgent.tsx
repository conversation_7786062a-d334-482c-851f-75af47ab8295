/**
 * FluxTrader Agent Management Component
 * Provides UI for creating, starting, stopping, and monitoring FluxTrader agents
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { agentService, type Agent, type AgentStatus } from '@/services/agentService';
import { useAuth } from '@/contexts/AuthContext';
import { useExchange } from '@/contexts/ExchangeContext';
import { websocketClient } from '@/services/websocketClient';
import { TradingEventsPanel } from './TradingEventsPanel';
import { PerformanceMetricsPanel } from './PerformanceMetricsPanel';
import { TradeHistoryPanel } from './TradeHistoryPanel';
import {
  Bot,
  Play,
  Pause,
  Square,
  Settings,
  Activity,
  DollarSign,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Target
} from 'lucide-react';

// Real-time trading data interfaces
interface TradingEvent {
  id: string;
  type: 'trade' | 'order' | 'signal' | 'analysis' | 'error';
  timestamp: string;
  symbol?: string;
  action?: 'BUY' | 'SELL' | 'HOLD';
  amount?: number;
  price?: number;
  profit?: number;
  message: string;
  confidence?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
}

interface PerformanceMetrics {
  totalPnl: number;
  dailyPnl: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  currentBalance: number;
  maxDrawdown: number;
  sharpeRatio: number;
  avgTradeProfit: number;
  lastUpdated: string;
}

interface FluxTraderAgentProps {
  className?: string;
}

export const FluxTraderAgent: React.FC<FluxTraderAgentProps> = ({ className }) => {
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentStatuses, setAgentStatuses] = useState<Record<string, AgentStatus>>({});
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [credentials, setCredentials] = useState({ hasCredentials: false, isMainnet: false });
  const [balance, setBalance] = useState({ balance: 0, available: 0, success: false });

  // Real-time data state
  const [tradingEvents, setTradingEvents] = useState<TradingEvent[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<Record<string, PerformanceMetrics>>({});
  const [tradeHistory, setTradeHistory] = useState<any[]>([]);
  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  // Refs for cleanup
  const statusIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Generate mock data for demonstration
  const generateMockData = useCallback((agentId: string) => {
    // Generate initial performance metrics
    const initialMetrics: PerformanceMetrics = {
      totalPnl: 0,
      dailyPnl: 0,
      winRate: 0,
      totalTrades: 0,
      successfulTrades: 0,
      currentBalance: 1000, // Starting balance
      maxDrawdown: 0,
      sharpeRatio: 0,
      avgTradeProfit: 0,
      lastUpdated: new Date().toISOString()
    };

    setPerformanceMetrics(prev => ({
      ...prev,
      [agentId]: initialMetrics
    }));

    // Generate initial trading event
    const initialEvent: TradingEvent = {
      id: `${Date.now()}_init`,
      type: 'signal',
      timestamp: new Date().toISOString(),
      message: 'FluxTrader agent initialized and ready for trading',
      status: 'completed'
    };

    setTradingEvents(prev => [initialEvent, ...prev]);
  }, []);

  const { toast } = useToast();
  const { user } = useAuth();
  const { hasConnectedExchange } = useExchange();

  // WebSocket event handlers
  const handleWebSocketConnected = useCallback(() => {
    console.log('🔌 [FluxTrader] WebSocket connected');
    setIsWebSocketConnected(true);
  }, []);

  const handleWebSocketDisconnected = useCallback(() => {
    console.log('🔌 [FluxTrader] WebSocket disconnected');
    setIsWebSocketConnected(false);
  }, []);

  const handleAgentUpdate = useCallback((message: any) => {
    if (message.agent_id && message.event && message.data) {
      console.log('📊 [FluxTrader] Agent update:', message);

      // Update agent status if status changed
      if (message.event === 'status_changed') {
        setAgentStatuses(prev => ({
          ...prev,
          [message.agent_id]: { ...prev[message.agent_id], ...message.data }
        }));
      }

      // Add trading event
      if (message.event === 'trading_event' || message.event === 'agent_event') {
        const event: TradingEvent = {
          id: `${Date.now()}_${Math.random()}`,
          type: message.data.type || 'signal',
          timestamp: message.data.timestamp || new Date().toISOString(),
          symbol: message.data.symbol,
          action: message.data.action,
          amount: message.data.amount,
          price: message.data.price,
          profit: message.data.profit,
          message: message.data.message || message.data.description || 'Agent event',
          confidence: message.data.confidence,
          status: message.data.status || 'completed'
        };

        setTradingEvents(prev => [event, ...prev.slice(0, 49)]); // Keep last 50 events
      }

      // Update performance metrics
      if (message.event === 'performance_update' || message.event === 'metrics_update') {
        setPerformanceMetrics(prev => ({
          ...prev,
          [message.agent_id]: {
            totalPnl: message.data.total_pnl || 0,
            dailyPnl: message.data.daily_pnl || 0,
            winRate: message.data.win_rate || 0,
            totalTrades: message.data.total_trades || 0,
            successfulTrades: message.data.successful_trades || 0,
            currentBalance: message.data.current_balance || 0,
            maxDrawdown: message.data.max_drawdown || 0,
            sharpeRatio: message.data.sharpe_ratio || 0,
            avgTradeProfit: message.data.avg_trade_profit || 0,
            lastUpdated: new Date().toISOString()
          }
        }));
      }

      // Add completed trades to history
      if (message.event === 'trade_completed') {
        const trade = {
          id: message.data.trade_id || `${Date.now()}_${Math.random()}`,
          symbol: message.data.symbol,
          side: message.data.side,
          amount: message.data.amount,
          price: message.data.price,
          total: message.data.total,
          profit: message.data.profit || 0,
          timestamp: message.data.timestamp || new Date().toISOString(),
          status: 'completed',
          confidence: message.data.confidence,
          strategy: message.data.strategy
        };

        setTradeHistory(prev => [trade, ...prev.slice(0, 99)]); // Keep last 100 trades
      }
    }
  }, []);

  // Setup WebSocket connection
  useEffect(() => {
    if (!mountedRef.current) return;

    // Setup WebSocket event listeners
    websocketClient.on('connected', handleWebSocketConnected);
    websocketClient.on('disconnected', handleWebSocketDisconnected);
    websocketClient.on('agent_update', handleAgentUpdate);
    websocketClient.on('trading_update', handleAgentUpdate);

    // Connect to WebSocket
    websocketClient.connectPortfolio().catch(console.error);

    return () => {
      websocketClient.off('connected', handleWebSocketConnected);
      websocketClient.off('disconnected', handleWebSocketDisconnected);
      websocketClient.off('agent_update', handleAgentUpdate);
      websocketClient.off('trading_update', handleAgentUpdate);
    };
  }, [handleWebSocketConnected, handleWebSocketDisconnected, handleAgentUpdate]);

  // Load agents and check prerequisites
  useEffect(() => {
    loadAgents();
    checkPrerequisites();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current);
      }
    };
  }, []);

  // Auto-refresh agent statuses every 5 seconds
  useEffect(() => {
    if (statusIntervalRef.current) {
      clearInterval(statusIntervalRef.current);
    }

    statusIntervalRef.current = setInterval(() => {
      if (mountedRef.current) {
        refreshAgentStatuses();
      }
    }, 5000);

    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current);
      }
    };
  }, [agents]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      const agentList = await agentService.getAgents();
      const fluxTraderAgents = agentList.filter(agent => 
        agent.id.includes('fluxtrader') || agent.strategy?.toLowerCase().includes('pump')
      );
      setAgents(fluxTraderAgents);
      
      // Load statuses for each agent
      for (const agent of fluxTraderAgents) {
        try {
          const status = await agentService.getAgentStatus(agent.id);
          setAgentStatuses(prev => ({ ...prev, [agent.id]: status }));
        } catch (error) {
          console.error(`Failed to load status for agent ${agent.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to load agents:', error);
      toast({
        title: "Error",
        description: "Failed to load trading agents",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const checkPrerequisites = async () => {
    try {
      const [credCheck, balanceCheck] = await Promise.all([
        agentService.checkCredentials(),
        agentService.checkBalance()
      ]);
      
      setCredentials(credCheck);
      setBalance(balanceCheck);
    } catch (error) {
      console.error('Failed to check prerequisites:', error);
    }
  };

  const refreshAgentStatuses = async () => {
    for (const agent of agents) {
      try {
        const status = await agentService.getAgentStatus(agent.id);
        setAgentStatuses(prev => ({ ...prev, [agent.id]: status }));
      } catch (error) {
        // Silently fail for status updates
      }
    }
  };

  const createFluxTraderAgent = async () => {
    if (!credentials.hasCredentials) {
      toast({
        title: "Missing Credentials",
        description: "Please add your Binance API credentials first",
        variant: "destructive"
      });
      return;
    }

    if (balance.available < 10) {
      toast({
        title: "Insufficient Balance",
        description: "Minimum $10 USDT balance recommended for trading",
        variant: "destructive"
      });
      return;
    }

    try {
      setCreating(true);
      const newAgent = await agentService.createFluxTraderAgent();
      
      toast({
        title: "Agent Created",
        description: `FluxTrader agent created successfully`,
        variant: "default"
      });
      
      await loadAgents();
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create FluxTrader agent",
        variant: "destructive"
      });
    } finally {
      setCreating(false);
    }
  };

  const startAgent = async (agentId: string) => {
    try {
      const result = await agentService.startAgent(agentId);

      if (result.success) {
        toast({
          title: "Agent Started",
          description: "FluxTrader agent is now actively trading",
          variant: "default"
        });

        await refreshAgentStatuses();

        // Navigate to dedicated FluxTrader dashboard
        navigate(`/dashboard/fluxtrader/${agentId}`);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Failed to start agent:', error);
      toast({
        title: "Start Failed",
        description: "Failed to start trading agent",
        variant: "destructive"
      });
    }
  };

  const stopAgent = async (agentId: string) => {
    try {
      const result = await agentService.stopAgent(agentId);
      
      if (result.success) {
        toast({
          title: "Agent Stopped",
          description: "FluxTrader agent has been stopped",
          variant: "default"
        });
        await refreshAgentStatuses();
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Failed to stop agent:', error);
      toast({
        title: "Stop Failed",
        description: "Failed to stop trading agent",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'starting': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'stopping': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'stopped': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'error': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Activity className="h-3 w-3" />;
      case 'starting': return <Clock className="h-3 w-3" />;
      case 'stopping': return <Clock className="h-3 w-3" />;
      case 'stopped': return <Square className="h-3 w-3" />;
      case 'error': return <AlertTriangle className="h-3 w-3" />;
      default: return <Square className="h-3 w-3" />;
    }
  };

  return (
    <Card className="glass-card hover:shadow-elevated transition-all duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-3">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-glow">
              <Bot className="w-6 h-6 text-white" />
            </div>
            FluxTrader AI Agent
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={loadAgents}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              size="sm"
              variant="premium"
              onClick={createFluxTraderAgent}
              disabled={creating || !credentials.hasCredentials}
              className="shadow-glow"
            >
              {creating ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Zap className="w-4 h-4 mr-2" />
              )}
              Create Agent
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Prerequisites Check */}
        <div className="space-y-3">
          <h4 className="font-medium text-foreground">System Status</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="flex items-center gap-2 p-3 rounded-lg bg-secondary/30">
              {credentials.hasCredentials ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-400" />
              )}
              <span className="text-sm">
                {credentials.hasCredentials ? 'Credentials Ready' : 'No Credentials'}
              </span>
              {credentials.isMainnet && (
                <Badge variant="destructive" className="text-xs">LIVE</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 p-3 rounded-lg bg-secondary/30">
              {balance.success && balance.available >= 10 ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-yellow-400" />
              )}
              <span className="text-sm">
                Balance: ${balance.available.toFixed(2)}
              </span>
            </div>
            
            <div className="flex items-center gap-2 p-3 rounded-lg bg-secondary/30">
              {hasConnectedExchange() ? (
                <Wifi className="h-4 w-4 text-green-400" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-400" />
              )}
              <span className="text-sm">
                {hasConnectedExchange() ? 'Exchange Connected' : 'No Exchange'}
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Agents List */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Active Agents ({agents.length})</h4>
          
          {agents.length === 0 ? (
            <Alert>
              <Bot className="h-4 w-4" />
              <AlertDescription>
                No FluxTrader agents found. Create your first agent to start automated trading.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-3">
              {agents.map((agent) => {
                const status = agentStatuses[agent.id];
                const isRunning = status?.status === 'running';
                
                return (
                  <div
                    key={agent.id}
                    className="p-4 rounded-lg border border-border/30 bg-gradient-glass backdrop-blur-sm hover:shadow-elevated transition-all duration-300"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Bot className="h-8 w-8 text-primary" />
                          {isRunning && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                          )}
                        </div>
                        <div>
                          <h5 className="font-medium text-foreground">{agent.name || agent.id}</h5>
                          <p className="text-sm text-muted-foreground">Pump & Dump Detection</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {status && (
                          <Badge className={getStatusColor(status.status)}>
                            {getStatusIcon(status.status)}
                            {status.status}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      {isRunning ? (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => stopAgent(agent.id)}
                          className="flex-1"
                        >
                          <Pause className="w-4 h-4 mr-2" />
                          Stop Trading
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="premium"
                          onClick={() => startAgent(agent.id)}
                          disabled={!credentials.hasCredentials}
                          className="flex-1 shadow-glow"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Start Trading
                        </Button>
                      )}
                      
                      <Button size="sm" variant="outline" className="aspect-square p-0 w-10">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Real-time Trading Dashboard */}
        {selectedAgent && (
          <>
            <Separator className="my-6" />

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-foreground flex items-center gap-2">
                  <Activity className="w-5 h-5 text-primary" />
                  Live Trading Dashboard
                  <Badge variant="secondary" className="ml-2">
                    {selectedAgent}
                  </Badge>
                </h4>

                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isWebSocketConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`} />
                  <span className="text-xs text-muted-foreground">
                    {isWebSocketConnected ? 'Live' : 'Disconnected'}
                  </span>
                </div>
              </div>

              {/* Performance Metrics */}
              <PerformanceMetricsPanel
                metrics={performanceMetrics[selectedAgent] || null}
                agentId={selectedAgent}
                className="w-full"
              />

              {/* Trading Events and Trade History Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <TradingEventsPanel
                  events={tradingEvents}
                  className="w-full"
                />

                <TradeHistoryPanel
                  trades={tradeHistory}
                  className="w-full"
                />
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
