/**
 * Quick Start Trading Component
 * One-click button to start FluxTrader agent with automatic setup
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAgents } from '@/hooks/useAgents';
import { useAuth } from '@/contexts/AuthContext';
import { useExchange } from '@/contexts/ExchangeContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Zap,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  TrendingUp,
  Shield,
  RefreshCw
} from 'lucide-react';

interface QuickStartTradingProps {
  className?: string;
}

export const QuickStartTrading: React.FC<QuickStartTradingProps> = ({ className }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  
  const { toast } = useToast();
  const { user } = useAuth();
  const { hasConnectedExchange } = useExchange();
  const {
    agents,
    credentials,
    balance,
    createFluxTraderAgent,
    startAgent,
    checkPrerequisites
  } = useAgents();

  // Check if there's already a running FluxTrader agent
  const hasRunningAgent = agents.some(agent => 
    agent.status === 'running' || agent.status === 'starting'
  );

  const canStartTrading = credentials.hasCredentials && 
                         balance.success && 
                         balance.available >= 10 && 
                         hasConnectedExchange();

  const handleQuickStart = async () => {
    if (!canStartTrading) {
      toast({
        title: "Prerequisites Not Met",
        description: "Please ensure you have credentials, sufficient balance, and exchange connection",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsStarting(true);

      // Check if we already have a FluxTrader agent
      let agentToStart = agents.find(agent => 
        agent.id.includes('fluxtrader') || agent.strategy?.toLowerCase().includes('pump')
      );

      // If no agent exists, create one
      if (!agentToStart) {
        toast({
          title: "Creating Agent",
          description: "Setting up your FluxTrader agent...",
          variant: "default"
        });

        agentToStart = await createFluxTraderAgent({
          trading_pairs: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
          risk_level: 'medium',
          leverage: 20,
          trade_amount_usdt: 4.0,
          enable_real_trades: true
        });

        if (!agentToStart) {
          throw new Error('Failed to create agent');
        }
      }

      // Start the agent
      const success = await startAgent(agentToStart.id);
      
      if (success) {
        toast({
          title: "Trading Started! 🚀",
          description: "FluxTrader is now actively monitoring markets and executing trades",
          variant: "default"
        });
        setIsOpen(false);
      }

    } catch (error) {
      console.error('Quick start failed:', error);
      toast({
        title: "Quick Start Failed",
        description: "Failed to start automated trading",
        variant: "destructive"
      });
    } finally {
      setIsStarting(false);
    }
  };

  const handleRefreshPrerequisites = async () => {
    await checkPrerequisites();
    toast({
      title: "Status Updated",
      description: "Prerequisites status has been refreshed",
      variant: "default"
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          size="lg"
          variant={hasRunningAgent ? "outline" : "premium"}
          disabled={hasRunningAgent}
          className={`shadow-glow hover:scale-105 transition-all duration-300 ${className}`}
        >
          {hasRunningAgent ? (
            <>
              <CheckCircle className="w-5 h-5 mr-2" />
              Trading Active
            </>
          ) : (
            <>
              <Zap className="w-5 h-5 mr-2" />
              Start Trading Now
            </>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[500px] glass-card">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Zap className="w-6 h-6 text-primary" />
            Quick Start Trading
          </DialogTitle>
          <DialogDescription>
            Start automated trading with FluxTrader AI in seconds. We'll handle the setup for you.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Prerequisites Check */}
          <div className="space-y-3">
            <h4 className="font-medium text-foreground flex items-center gap-2">
              <Shield className="w-4 h-4" />
              System Status
              <Button
                size="sm"
                variant="ghost"
                onClick={handleRefreshPrerequisites}
                className="ml-auto"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </h4>
            
            <div className="space-y-2">
              {/* Credentials Check */}
              <div className="flex items-center justify-between p-3 rounded-lg bg-secondary/30">
                <div className="flex items-center gap-2">
                  {credentials.hasCredentials ? (
                    <CheckCircle className="h-4 w-4 text-green-400" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                  )}
                  <span className="text-sm">Binance API Credentials</span>
                </div>
                <div className="flex items-center gap-2">
                  {credentials.hasCredentials && (
                    <Badge variant={credentials.isMainnet ? "destructive" : "secondary"} className="text-xs">
                      {credentials.isMainnet ? 'LIVE' : 'TESTNET'}
                    </Badge>
                  )}
                  <span className="text-xs text-muted-foreground">
                    {credentials.hasCredentials ? 'Ready' : 'Missing'}
                  </span>
                </div>
              </div>

              {/* Balance Check */}
              <div className="flex items-center justify-between p-3 rounded-lg bg-secondary/30">
                <div className="flex items-center gap-2">
                  {balance.success && balance.available >= 10 ? (
                    <CheckCircle className="h-4 w-4 text-green-400" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  )}
                  <span className="text-sm">Account Balance</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="w-3 h-3" />
                  <span className="text-xs">
                    ${balance.available.toFixed(2)} available
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Trading Configuration Preview */}
          <div className="space-y-3">
            <h4 className="font-medium text-foreground flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Trading Configuration
            </h4>
            
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="p-3 rounded-lg bg-secondary/30">
                <p className="text-muted-foreground">Strategy</p>
                <p className="font-medium">Pump & Dump Detection</p>
              </div>
              <div className="p-3 rounded-lg bg-secondary/30">
                <p className="text-muted-foreground">Trade Amount</p>
                <p className="font-medium">$4 USDT per position</p>
              </div>
              <div className="p-3 rounded-lg bg-secondary/30">
                <p className="text-muted-foreground">Leverage</p>
                <p className="font-medium">20x</p>
              </div>
              <div className="p-3 rounded-lg bg-secondary/30">
                <p className="text-muted-foreground">Risk Level</p>
                <p className="font-medium">Medium</p>
              </div>
            </div>
          </div>

          {/* Warning */}
          {credentials.isMainnet && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> You are using LIVE credentials. Real money will be traded.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="premium"
              onClick={handleQuickStart}
              disabled={!canStartTrading || isStarting}
              className="flex-1 shadow-glow"
            >
              {isStarting ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Starting...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Start Trading
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
