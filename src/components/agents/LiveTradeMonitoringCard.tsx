import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Target,
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  Timer
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TradeExecutionData {
  trade_id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  order_type: string;
  status: 'PENDING' | 'FILLED' | 'CANCELLED' | 'FAILED' | 'PARTIALLY_FILLED';
  filled_quantity?: number;
  average_price?: number;
  commission?: number;
  profit_loss?: number;
  order_id: string;
  execution_time?: string;
  error_message?: string;
  timestamp: string;
}

interface LiveTradeMonitoringCardProps {
  trades: TradeExecutionData[];
  className?: string;
}

export function LiveTradeMonitoringCard({ trades, className }: LiveTradeMonitoringCardProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    }).format(value);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatQuantity = (quantity: number) => {
    return quantity.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 8
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'FILLED':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'PARTIALLY_FILLED':
        return <Timer className="w-4 h-4 text-blue-500" />;
      case 'CANCELLED':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      case 'FAILED':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'text-yellow-500 border-yellow-500 bg-yellow-500/10';
      case 'FILLED':
        return 'text-green-500 border-green-500 bg-green-500/10';
      case 'PARTIALLY_FILLED':
        return 'text-blue-500 border-blue-500 bg-blue-500/10';
      case 'CANCELLED':
        return 'text-orange-500 border-orange-500 bg-orange-500/10';
      case 'FAILED':
        return 'text-red-500 border-red-500 bg-red-500/10';
      default:
        return 'text-muted-foreground border-muted-foreground bg-muted/10';
    }
  };

  const getSideIcon = (side: string) => {
    return side === 'BUY' ? (
      <ArrowUpRight className="w-4 h-4 text-green-500" />
    ) : (
      <ArrowDownRight className="w-4 h-4 text-red-500" />
    );
  };

  const getSideColor = (side: string) => {
    return side === 'BUY' ? 'text-green-500 border-green-500' : 'text-red-500 border-red-500';
  };

  // Calculate summary statistics
  const totalTrades = trades.length;
  const filledTrades = trades.filter(t => t.status === 'FILLED').length;
  const pendingTrades = trades.filter(t => t.status === 'PENDING').length;
  const totalPnL = trades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0);
  const totalCommission = trades.reduce((sum, trade) => sum + (trade.commission || 0), 0);

  return (
    <Card className={cn('glass-card', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Activity className="w-5 h-5 text-primary" />
            Live Trade Monitoring
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse" />
            <span className="text-xs text-muted-foreground">Live</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <Target className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-muted-foreground">Total Trades</span>
            </div>
            <p className="text-lg font-semibold text-foreground">{totalTrades}</p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span className="text-xs text-muted-foreground">Filled</span>
            </div>
            <p className="text-lg font-semibold text-foreground">{filledTrades}</p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <Loader2 className="w-4 h-4 text-yellow-400" />
              <span className="text-xs text-muted-foreground">Pending</span>
            </div>
            <p className="text-lg font-semibold text-foreground">{pendingTrades}</p>
          </div>

          <div className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-1">
              <DollarSign className="w-4 h-4 text-primary" />
              <span className="text-xs text-muted-foreground">Total P&L</span>
            </div>
            <p className={`text-lg font-semibold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatCurrency(totalPnL)}
            </p>
          </div>
        </div>

        {/* Recent Trades */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Zap className="w-4 h-4 text-primary" />
            Recent Trades
          </h4>
          
          <ScrollArea className="h-[300px] pr-4">
            {trades.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
                <Activity className="w-8 h-8 mb-2 opacity-50" />
                <p className="text-sm">No trades executed yet</p>
                <p className="text-xs">Live trades will appear here when executed</p>
              </div>
            ) : (
              <div className="space-y-3">
                {trades.slice().reverse().map((trade) => (
                  <div
                    key={trade.trade_id}
                    className="p-3 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:shadow-elevated hover:border-primary/30 transition-all duration-300"
                  >
                    {/* Trade Header */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getSideIcon(trade.side)}
                        <Badge variant="outline" className={getSideColor(trade.side)}>
                          {trade.side}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {trade.symbol}
                        </Badge>
                        <Badge variant="outline" className={getStatusColor(trade.status)}>
                          {getStatusIcon(trade.status)}
                          {trade.status}
                        </Badge>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(trade.timestamp)}
                      </span>
                    </div>

                    {/* Trade Details */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div>
                        <span className="text-muted-foreground">Quantity:</span>
                        <span className="ml-1 font-medium text-foreground">
                          {formatQuantity(trade.quantity)}
                        </span>
                      </div>
                      
                      <div>
                        <span className="text-muted-foreground">Price:</span>
                        <span className="ml-1 font-medium text-foreground">
                          {formatCurrency(trade.price)}
                        </span>
                      </div>

                      {trade.filled_quantity && (
                        <div>
                          <span className="text-muted-foreground">Filled:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatQuantity(trade.filled_quantity)}
                          </span>
                        </div>
                      )}

                      {trade.average_price && (
                        <div>
                          <span className="text-muted-foreground">Avg Price:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatCurrency(trade.average_price)}
                          </span>
                        </div>
                      )}

                      {trade.commission && (
                        <div>
                          <span className="text-muted-foreground">Commission:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatCurrency(trade.commission)}
                          </span>
                        </div>
                      )}

                      {trade.profit_loss !== undefined && (
                        <div>
                          <span className="text-muted-foreground">P&L:</span>
                          <span className={`ml-1 font-medium flex items-center gap-1 ${
                            trade.profit_loss >= 0 ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {trade.profit_loss >= 0 ? (
                              <TrendingUp className="w-3 h-3" />
                            ) : (
                              <TrendingDown className="w-3 h-3" />
                            )}
                            {formatCurrency(Math.abs(trade.profit_loss))}
                          </span>
                        </div>
                      )}

                      <div>
                        <span className="text-muted-foreground">Order ID:</span>
                        <span className="ml-1 font-medium text-foreground text-xs">
                          {trade.order_id.slice(-8)}
                        </span>
                      </div>

                      {trade.execution_time && (
                        <div>
                          <span className="text-muted-foreground">Executed:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {formatTime(trade.execution_time)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Error Message */}
                    {trade.error_message && (
                      <div className="mt-2 p-2 rounded border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
                        <p className="text-xs text-red-600 dark:text-red-400">
                          Error: {trade.error_message}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Commission Summary */}
        {totalCommission > 0 && (
          <div className="p-3 rounded-lg border border-border/30 bg-gradient-glass backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Total Commission Paid:</span>
              <span className="text-sm font-medium text-foreground">
                {formatCurrency(totalCommission)}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
