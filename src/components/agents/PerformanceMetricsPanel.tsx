import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Target,
  Activity,
  Zap,
  Shield
} from 'lucide-react';

interface PerformanceMetrics {
  totalPnl: number;
  dailyPnl: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  currentBalance: number;
  maxDrawdown: number;
  sharpeRatio: number;
  avgTradeProfit: number;
  lastUpdated: string;
}

interface PerformanceMetricsPanelProps {
  metrics: PerformanceMetrics | null;
  agentId: string;
  className?: string;
}

export function PerformanceMetricsPanel({ metrics, agentId, className }: PerformanceMetricsPanelProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const getPnlColor = (value: number) => {
    return value >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getPnlIcon = (value: number) => {
    return value >= 0 ?
      <TrendingUp className="w-4 h-4 text-green-400" /> :
      <TrendingDown className="w-4 h-4 text-red-400" />;
  };

  const getWinRateColor = (winRate: number) => {
    if (winRate >= 70) return 'text-green-400';
    if (winRate >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatLastUpdated = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    if (diffSeconds < 86400) return `${Math.floor(diffSeconds / 3600)}h ago`;
    return date.toLocaleDateString();
  };

  if (!metrics) {
    return (
      <Card className={`glass-card ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            Performance Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <BarChart3 className="w-8 h-8 mb-2 opacity-50" />
            <p className="text-sm">No performance data available</p>
            <p className="text-xs">Metrics will appear when the agent starts trading</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`glass-card ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          Performance Metrics
          <Badge variant="secondary" className="ml-auto text-xs">
            {formatLastUpdated(metrics.lastUpdated)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Total P&L */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-muted-foreground">Total P&L</span>
              </div>
              {getPnlIcon(metrics.totalPnl)}
            </div>
            <div className={`text-2xl font-bold ${getPnlColor(metrics.totalPnl)}`}>
              {formatCurrency(metrics.totalPnl)}
            </div>
          </div>

          {/* Daily P&L */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-muted-foreground">Daily P&L</span>
              </div>
              {getPnlIcon(metrics.dailyPnl)}
            </div>
            <div className={`text-2xl font-bold ${getPnlColor(metrics.dailyPnl)}`}>
              {formatCurrency(metrics.dailyPnl)}
            </div>
          </div>

          {/* Current Balance */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-muted-foreground">Balance</span>
            </div>
            <div className="text-2xl font-bold text-foreground">
              {formatCurrency(metrics.currentBalance)}
            </div>
          </div>

          {/* Win Rate */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-2">
              <Target className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-muted-foreground">Win Rate</span>
            </div>
            <div className={`text-2xl font-bold ${getWinRateColor(metrics.winRate)}`}>
              {formatPercentage(metrics.winRate)}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {metrics.successfulTrades}/{metrics.totalTrades} trades
            </div>
          </div>

          {/* Average Trade Profit */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-muted-foreground">Avg Trade</span>
            </div>
            <div className={`text-2xl font-bold ${getPnlColor(metrics.avgTradeProfit)}`}>
              {formatCurrency(metrics.avgTradeProfit)}
            </div>
          </div>

          {/* Max Drawdown */}
          <div className="p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
            <div className="flex items-center gap-2 mb-2">
              <TrendingDown className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-muted-foreground">Max Drawdown</span>
            </div>
            <div className="text-2xl font-bold text-red-400">
              {formatPercentage(Math.abs(metrics.maxDrawdown))}
            </div>
          </div>
        </div>

        {/* Additional Metrics Row */}
        <div className="mt-4 p-4 rounded-lg border border-border/30 bg-card/50 backdrop-blur-sm hover:border-primary/30 transition-all duration-300">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Total Trades:</span>
              <span className="ml-2 font-medium text-foreground">{metrics.totalTrades}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Successful:</span>
              <span className="ml-2 font-medium text-green-500">{metrics.successfulTrades}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Sharpe Ratio:</span>
              <span className="ml-2 font-medium text-foreground">{metrics.sharpeRatio.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
