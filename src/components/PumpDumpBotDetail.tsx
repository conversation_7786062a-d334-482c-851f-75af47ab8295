import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Play, 
  Pause, 
  Settings, 
  TrendingUp, 
  TrendingDown,
  Activity,
  DollarSign,
  Target,
  Clock,
  BarChart3,
  Zap,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/services/api';

interface Trade {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  total: number;
  pnl: number;
  timestamp: number;
  status: 'completed' | 'pending' | 'failed';
}

interface BotMetrics {
  totalPnl: number;
  winRate: number;
  totalTrades: number;
  dailyProfit: number;
  currentBalance: number;
  maxDrawdown: number;
  sharpeRatio: number;
  avgTradeProfit: number;
}

interface PumpDumpBotDetailProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PumpDumpBotDetail({ isOpen, onClose }: PumpDumpBotDetailProps) {
  const { toast } = useToast();
  const [botStatus, setBotStatus] = useState<'active' | 'stopped' | 'starting' | 'stopping'>('stopped');
  const [loading, setLoading] = useState(false);
  const [metrics, setMetrics] = useState<BotMetrics>({
    totalPnl: 0,
    winRate: 0,
    totalTrades: 0,
    dailyProfit: 0,
    currentBalance: 0,
    maxDrawdown: 0,
    sharpeRatio: 0,
    avgTradeProfit: 0
  });
  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);

  // Fetch bot status from API
  const fetchBotStatus = async () => {
    try {
      const response = await apiClient.get('/api/v1/agents/pump_n_dump_agent/status');
      if (response.data && response.data.data) {
        setBotStatus(response.data.data.status);
      }
    } catch (error) {
      console.error('Failed to fetch bot status:', error);
    }
  };

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!isOpen) return;

    // Initial data fetch
    fetchBotStatus();
    fetchBotMetrics();
    fetchRecentTrades();

    // Set up real-time updates every second
    const interval = setInterval(() => {
      fetchBotStatus();
      fetchBotMetrics();
      fetchRecentTrades();
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [isOpen]);

  const fetchBotMetrics = async () => {
    try {
      const response = await apiClient.get('/api/v1/agents/pump_n_dump_agent/metrics');
      if (response.data && response.data.metrics) {
        setMetrics(response.data.metrics);
      }
    } catch (error) {
      console.error('Failed to fetch bot metrics:', error);
      // Keep existing metrics on error
    }
  };

  const fetchRecentTrades = async () => {
    try {
      const response = await apiClient.get('/api/v1/agents/pump_n_dump_agent/trades');
      if (response.data && response.data.trades) {
        setRecentTrades(response.data.trades);
      }
    } catch (error) {
      console.error('Failed to fetch recent trades:', error);
      // Keep existing trades on error
    }
  };

  const handleStartBot = async () => {
    setLoading(true);
    setBotStatus('starting');
    try {
      const response = await apiClient.post('/api/v1/agents/pump_n_dump_agent/start');
      if (response.data.status === 'success') {
        setBotStatus('active');
        toast({
          title: "Pump n Dump Agent Started",
          description: "Bot is now actively scanning for pump and dump patterns",
        });
      }
    } catch (error) {
      console.error('Failed to start bot:', error);
      setBotStatus('stopped');
      toast({
        title: "Error",
        description: "Failed to start Pump n Dump Agent",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStopBot = async () => {
    setLoading(true);
    setBotStatus('stopping');
    try {
      const response = await apiClient.post('/api/v1/agents/pump_n_dump_agent/stop');
      if (response.data.status === 'success') {
        setBotStatus('stopped');
        toast({
          title: "Pump n Dump Agent Stopped",
          description: "Bot has been stopped successfully",
        });
      }
    } catch (error) {
      console.error('Failed to stop bot:', error);
      setBotStatus('active');
      toast({
        title: "Error",
        description: "Failed to stop Pump n Dump Agent",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success text-success-foreground';
      case 'stopped':
        return 'bg-muted text-muted-foreground';
      case 'starting':
      case 'stopping':
        return 'bg-warning text-warning-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-background border border-border rounded-lg shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-foreground">Pump n Dump Agent</h2>
              <p className="text-muted-foreground">Advanced pump & dump pattern detection</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge className={getStatusColor(botStatus)}>
              {botStatus.charAt(0).toUpperCase() + botStatus.slice(1)}
            </Badge>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Control Panel */}
          <div className="mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Bot Control
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  {botStatus === 'active' ? (
                    <Button
                      variant="destructive"
                      onClick={handleStopBot}
                      disabled={loading}
                      className="flex items-center gap-2"
                    >
                      <Pause className="w-4 h-4" />
                      {loading ? 'Stopping...' : 'Stop Bot'}
                    </Button>
                  ) : (
                    <Button
                      onClick={handleStartBot}
                      disabled={loading}
                      className="flex items-center gap-2 bg-success hover:bg-success/90"
                    >
                      <Play className="w-4 h-4" />
                      {loading ? 'Starting...' : 'Start Bot'}
                    </Button>
                  )}
                  <Button variant="outline" className="flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Configure
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-4 h-4 text-success" />
                  <span className="text-sm text-muted-foreground">Total P&L</span>
                </div>
                <div className="text-2xl font-bold text-success">
                  {formatCurrency(metrics.totalPnl)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="w-4 h-4 text-primary" />
                  <span className="text-sm text-muted-foreground">Win Rate</span>
                </div>
                <div className="text-2xl font-bold text-foreground">
                  {metrics.winRate.toFixed(1)}%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="w-4 h-4 text-warning" />
                  <span className="text-sm text-muted-foreground">Total Trades</span>
                </div>
                <div className="text-2xl font-bold text-foreground">
                  {metrics.totalTrades}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-success" />
                  <span className="text-sm text-muted-foreground">Daily Profit</span>
                </div>
                <div className="text-2xl font-bold text-success">
                  {formatCurrency(metrics.dailyProfit)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Tabs */}
          <Tabs defaultValue="trades" className="space-y-4">
            <TabsList>
              <TabsTrigger value="trades">Recent Trades</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="trades" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Trade Executions</CardTitle>
                </CardHeader>
                <CardContent>
                  {recentTrades.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No trades executed yet. Start the bot to begin trading.
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {recentTrades.map((trade) => (
                        <div
                          key={trade.id}
                          className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <Badge variant={trade.side === 'BUY' ? 'default' : 'destructive'}>
                              {trade.side}
                            </Badge>
                            <span className="font-medium">{trade.symbol}</span>
                            <span className="text-sm text-muted-foreground">
                              {trade.quantity} @ {formatCurrency(trade.price)}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className={`font-medium ${trade.pnl >= 0 ? 'text-success' : 'text-destructive'}`}>
                              {trade.pnl >= 0 ? '+' : ''}{formatCurrency(trade.pnl)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatTimestamp(trade.timestamp)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Risk Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Max Drawdown</span>
                      <span className="font-medium">{metrics.maxDrawdown.toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Sharpe Ratio</span>
                      <span className="font-medium">{metrics.sharpeRatio.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Avg Trade Profit</span>
                      <span className="font-medium">{formatCurrency(metrics.avgTradeProfit)}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Current Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Current Balance</span>
                      <span className="font-medium">{formatCurrency(metrics.currentBalance)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status</span>
                      <Badge className={getStatusColor(botStatus)}>
                        {botStatus.charAt(0).toUpperCase() + botStatus.slice(1)}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Bot Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    Configuration panel coming soon...
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
