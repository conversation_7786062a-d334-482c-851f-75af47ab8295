import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Loader2, Mail, Lock, User } from 'lucide-react';

import { useAuth } from '@/contexts/AuthContext';
import { registerSchema, type RegisterFormData } from '@/lib/validations/auth';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface RegisterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToSignIn: () => void;
}

export const RegisterModal: React.FC<RegisterModalProps> = ({
  isOpen,
  onClose,
  onSwitchToSignIn,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const { register: registerUser, isLoading } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterFormData) => {
    setAuthError(null);
    
    const result = await registerUser({
      name: data.name,
      email: data.email,
      password: data.password,
    });
    
    if (result.success) {
      reset();
      onClose();
      navigate('/dashboard');
    } else {
      setAuthError(result.error || 'Registration failed');
    }
  };

  const handleClose = () => {
    reset();
    setAuthError(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md auth-modal border-card-border/60">
        <DialogHeader className="space-y-3">
          <DialogTitle className="auth-title">
            Get Started
          </DialogTitle>
          <DialogDescription className="auth-subtitle">
            Create your Kamikaze trading account
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5 mt-6">
          {authError && (
            <div className="auth-alert">
              {authError}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="name" className="auth-label">
              Full Name
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="name"
                type="text"
                placeholder="Enter your full name"
                className="pl-10 auth-input"
                {...register('name')}
              />
            </div>
            {errors.name && (
              <p className="auth-error">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="auth-label">
              Email
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                className="pl-10 auth-input"
                {...register('email')}
              />
            </div>
            {errors.email && (
              <p className="auth-error">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="auth-label">
              Password
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Create a password"
                className="pl-10 pr-10 auth-input"
                {...register('password')}
              />
              <button
                type="button"
                className="auth-toggle-button"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="auth-error">{errors.password.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="auth-label">
              Confirm Password
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your password"
                className="pl-10 pr-10 auth-input"
                {...register('confirmPassword')}
              />
              <button
                type="button"
                className="auth-toggle-button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide password" : "Show password"}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="auth-error">{errors.confirmPassword.message}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full auth-button mt-6"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="auth-loading">
                <Loader2 className="h-4 w-4 spinner" />
                Creating Account...
              </span>
            ) : (
              'Create Account'
            )}
          </Button>

          <div className="text-center mt-6">
            <p className="text-sm auth-footer-text">
              Already have an account?{' '}
              <button
                type="button"
                className="auth-footer-link"
                onClick={onSwitchToSignIn}
              >
                Sign in here
              </button>
            </p>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
