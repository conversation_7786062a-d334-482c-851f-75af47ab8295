import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Loader2, Mail, Lock } from 'lucide-react';

import { useAuth } from '@/contexts/AuthContext';
import { loginSchema, type LoginFormData } from '@/lib/validations/auth';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface SignInModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToRegister: () => void;
}

export const SignInModal: React.FC<SignInModalProps> = ({
  isOpen,
  onClose,
  onSwitchToRegister,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const { login, isLoading } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setAuthError(null);
    
    const result = await login(data.email, data.password);
    
    if (result.success) {
      reset();
      onClose();
      navigate('/dashboard');
    } else {
      setAuthError(result.error || 'Login failed');
    }
  };

  const handleClose = () => {
    reset();
    setAuthError(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md auth-modal border-card-border/60">
        <DialogHeader className="space-y-3">
          <DialogTitle className="auth-title">
            Welcome Back
          </DialogTitle>
          <DialogDescription className="auth-subtitle">
            Sign in to your Kamikaze trading account
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5 mt-6">
          {authError && (
            <div className="auth-alert">
              {authError}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="auth-label">
              Email
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                className="pl-10 auth-input"
                {...register('email')}
              />
            </div>
            {errors.email && (
              <p className="auth-error">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="auth-label">
              Password
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 auth-icon" />
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                className="pl-10 pr-10 auth-input"
                {...register('password')}
              />
              <button
                type="button"
                className="auth-toggle-button"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="auth-error">{errors.password.message}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full auth-button mt-6"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="auth-loading">
                <Loader2 className="h-4 w-4 spinner" />
                Signing In...
              </span>
            ) : (
              'Sign In'
            )}
          </Button>

          <div className="text-center mt-6">
            <p className="text-sm auth-footer-text">
              Don't have an account?{' '}
              <button
                type="button"
                className="auth-footer-link"
                onClick={onSwitchToRegister}
              >
                Sign up here
              </button>
            </p>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
