import { useState } from "react";
import { BarChart3, <PERSON><PERSON>, T<PERSON>dingUp, <PERSON>tings, CreditCard, History, Home, Target, Brain, Shield, TestTube, AlertTriangle } from "lucide-react";
import { useLocation, Link } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { useExchange } from "@/contexts/ExchangeContext";

const mainItems = [
  { title: "Dashboard", url: "/dashboard", icon: Home, color: "text-primary", bgColor: "bg-primary/10", borderColor: "border-primary/20" },
  { title: "Trading Bots", url: "/dashboard/bots", icon: Bot, color: "text-accent-teal", bgColor: "bg-accent-teal/10", borderColor: "border-accent-teal/20" },
  { title: "Strategies", url: "/dashboard/strategies", icon: Target, color: "text-accent-purple", bgColor: "bg-accent-purple/10", borderColor: "border-accent-purple/20" },
  { title: "Portfolio", url: "/dashboard/portfolio", icon: TrendingUp, color: "text-success", bgColor: "bg-success/10", borderColor: "border-success/20" },
  { title: "Trading History", url: "/dashboard/history", icon: History, color: "text-warning", bgColor: "bg-warning/10", borderColor: "border-warning/20" },
];

const toolsItems = [
  { title: "Analytics", url: "/dashboard/analytics", icon: BarChart3, color: "text-chart-1", bgColor: "bg-chart-1/10", borderColor: "border-chart-1/20" },
  { title: "Risk Management", url: "/dashboard/risk", icon: Shield, color: "text-destructive", bgColor: "bg-destructive/10", borderColor: "border-destructive/20" },
  { title: "Assistant", url: "/dashboard/assistant", icon: Brain, color: "text-accent-purple", bgColor: "bg-accent-purple/10", borderColor: "border-accent-purple/20" },
];

const settingsItems = [
  { title: "Account", url: "/dashboard/account", icon: CreditCard, color: "text-accent-orange", bgColor: "bg-accent-orange/10", borderColor: "border-accent-orange/20" },
  { title: "Settings", url: "/dashboard/settings", icon: Settings, color: "text-muted-foreground", bgColor: "bg-muted/10", borderColor: "border-muted/20" },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const { hasConnectedExchange, isTestnetMode } = useExchange();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === "collapsed";

  const isTestnet = hasConnectedExchange() ? isTestnetMode() : false;

  const isActive = (path: string) => currentPath === path;
  const getNavCls = (path: string) =>
    isActive(path)
      ? "glass-card bg-gradient-to-r from-sidebar-primary to-sidebar-primary/90 text-sidebar-primary-foreground font-medium border border-sidebar-primary/40 shadow-elevated"
      : "glass-card hover:shadow-elevated hover:bg-sidebar-accent/20 text-sidebar-accent-foreground hover:text-sidebar-foreground hover:border-sidebar-primary/30 transition-all duration-300";

  return (
    <Sidebar
      className={`transition-all duration-300 ease-in-out ${collapsed ? "w-16" : "w-64"} border-r border-sidebar-border bg-sidebar`}
      collapsible="icon"
    >
      <SidebarContent className="py-6 overflow-y-auto bg-sidebar text-sidebar-foreground relative transition-all duration-300 ease-in-out">
        {/* Subtle background overlay for depth */}
        <div className="absolute inset-0 bg-gradient-to-b from-sidebar-accent/5 via-transparent to-sidebar-accent/10 pointer-events-none transition-opacity duration-300 ease-in-out"></div>
        <div className="relative z-10 transition-all duration-300 ease-in-out">
        {/* Premium Logo */}
        <div className={`glass-card sidebar-logo-enhanced mx-3 py-4 rounded-xl transition-all duration-300 ease-in-out ${collapsed ? 'px-2 mb-4' : 'px-4 mb-6'}`}>
          <div className={`flex items-center transition-all duration-300 ease-in-out ${collapsed ? 'justify-center' : 'gap-3'}`}>
            {/* Enhanced Bot Icon with consistent sidebar theming */}
            <div className="w-10 h-10 bg-gradient-to-br from-sidebar-primary via-sidebar-primary/80 to-sidebar-primary/60 rounded-xl flex items-center justify-center shadow-lg sidebar-icon-ring transition-all duration-300 relative">
              <Bot className="w-6 h-6 text-sidebar-primary-foreground drop-shadow-sm" />
              {/* Live indicator for collapsed state */}
              {collapsed && (
                <div className="absolute -top-1 -right-1 w-3 h-3 live-indicator-collapsed rounded-full animate-pulse"></div>
              )}
            </div>
            {!collapsed && (
              <div className="flex flex-col justify-center min-h-[2.5rem] flex-1">
                {/* Main brand name with enhanced contrast */}
                <span className="text-xl font-bold text-sidebar-foreground leading-tight tracking-tight brand-text-shadow">Kamikaze</span>
                {/* Environment status indicator */}
                {hasConnectedExchange() ? (
                  <div className="flex items-center gap-1.5 mt-1">
                    {isTestnet ? (
                      <>
                        <TestTube className="w-3 h-3 text-orange-400" />
                        <div className="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse shadow-sm"></div>
                        <span className="text-xs text-orange-400 font-semibold">TESTNET</span>
                      </>
                    ) : (
                      <>
                        <div className="w-1.5 h-1.5 bg-success rounded-full animate-pulse shadow-sm"></div>
                        <span className="text-xs text-success font-semibold live-status-text">LIVE</span>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-1.5 mt-1">
                    <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full"></div>
                    <span className="text-xs text-muted-foreground font-semibold">OFFLINE</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Trading Status Card */}
        {!collapsed && (
          <div className="mx-3 mb-6 glass-card hover:shadow-elevated transition-all duration-300 p-4 rounded-xl bg-sidebar-accent/15 border border-sidebar-border hover:border-sidebar-primary/30">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-sidebar-foreground">Portfolio</span>
                <span className="text-sm font-bold text-success">+12.5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-sidebar-accent-foreground">Active Bots</span>
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-sidebar-primary rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-sidebar-primary">4</span>
                </div>
              </div>
              <div className="w-full bg-sidebar-accent/30 rounded-full h-1.5 border border-sidebar-border">
                <div className="bg-gradient-to-r from-sidebar-primary to-sidebar-primary/80 h-1.5 rounded-full w-3/4"></div>
              </div>
            </div>
          </div>
        )}

        {/* Main Navigation */}
        <SidebarGroup>
          {!collapsed && (
            <SidebarGroupLabel className="text-sidebar-primary text-xs uppercase tracking-wider px-4 mb-3 font-semibold border-l-2 border-sidebar-primary/40 pl-3">
              Main
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu className={`space-y-2 transition-all duration-300 ease-in-out ${collapsed ? 'px-0 flex flex-col items-center' : 'px-3'}`}>
              {mainItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`transition-all duration-300 ease-in-out rounded-xl glass-interactive ${getNavCls(item.url)} ${!collapsed ? 'w-full justify-start h-11' : 'w-12 h-12 justify-center'}`}
                  >
                    <Link to={item.url} className={`flex items-center transition-all duration-300 rounded-xl ${!collapsed ? 'gap-3 p-3' : 'justify-center w-12 h-12 p-0'}`}>
                      <div className={`${!collapsed ? 'w-8 h-8' : 'w-6 h-6'} rounded-lg flex items-center justify-center ${isActive(item.url) ? 'bg-sidebar-primary-foreground/20' : item.bgColor} ${isActive(item.url) ? 'border-sidebar-primary-foreground/30' : item.borderColor} border transition-all duration-300`}>
                        <item.icon className={`${!collapsed ? 'w-4 h-4' : 'w-3.5 h-3.5'} flex-shrink-0 ${isActive(item.url) ? 'text-sidebar-primary-foreground' : item.color} transition-all duration-300`} />
                      </div>
                      {!collapsed && (
                        <div className="flex flex-col transition-all duration-300 ease-in-out opacity-100 animate-in fade-in slide-in-from-left-2">
                          <span className="text-sm font-medium transition-all duration-300 ease-in-out">{item.title}</span>

                        </div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Tools */}
        <SidebarGroup className="mt-6">
          {!collapsed && (
            <SidebarGroupLabel className="text-sidebar-primary text-xs uppercase tracking-wider px-4 mb-3 font-semibold border-l-2 border-sidebar-primary/40 pl-3">
              Tools
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu className={`space-y-2 transition-all duration-300 ease-in-out ${collapsed ? 'px-0 flex flex-col items-center' : 'px-3'}`}>
              {toolsItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`transition-all duration-300 ease-in-out rounded-xl glass-interactive ${getNavCls(item.url)} ${!collapsed ? 'w-full justify-start h-11' : 'w-12 h-12 justify-center'}`}
                  >
                    <Link to={item.url} className={`flex items-center transition-all duration-300 rounded-xl ${!collapsed ? 'gap-3 p-3' : 'justify-center w-12 h-12 p-0'}`}>
                      <div className={`${!collapsed ? 'w-8 h-8' : 'w-6 h-6'} rounded-lg flex items-center justify-center ${isActive(item.url) ? 'bg-sidebar-primary-foreground/20' : item.bgColor} ${isActive(item.url) ? 'border-sidebar-primary-foreground/30' : item.borderColor} border transition-all duration-300`}>
                        <item.icon className={`${!collapsed ? 'w-4 h-4' : 'w-3.5 h-3.5'} flex-shrink-0 ${isActive(item.url) ? 'text-sidebar-primary-foreground' : item.color} transition-all duration-300`} />
                      </div>
                      {!collapsed && (
                        <div className="flex flex-col transition-all duration-300 ease-in-out opacity-100 animate-in fade-in slide-in-from-left-2">
                          <span className="text-sm font-medium transition-all duration-300 ease-in-out">{item.title}</span>

                        </div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Settings */}
        <SidebarGroup className="mt-auto">
          {!collapsed && (
            <SidebarGroupLabel className="text-sidebar-primary text-xs uppercase tracking-wider px-4 mb-3 font-semibold border-l-2 border-sidebar-primary/40 pl-3">
              Account
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu className={`space-y-2 transition-all duration-300 ease-in-out ${collapsed ? 'px-0 flex flex-col items-center' : 'px-3'}`}>
              {settingsItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`transition-all duration-300 ease-in-out rounded-xl glass-interactive ${getNavCls(item.url)} ${!collapsed ? 'w-full justify-start h-11' : 'w-12 h-12 justify-center'}`}
                  >
                    <Link to={item.url} className={`flex items-center transition-all duration-300 rounded-xl ${!collapsed ? 'gap-3 p-3' : 'justify-center w-12 h-12 p-0'}`}>
                      <div className={`${!collapsed ? 'w-8 h-8' : 'w-6 h-6'} rounded-lg flex items-center justify-center ${isActive(item.url) ? 'bg-sidebar-primary-foreground/20' : item.bgColor} ${isActive(item.url) ? 'border-sidebar-primary-foreground/30' : item.borderColor} border transition-all duration-300`}>
                        <item.icon className={`${!collapsed ? 'w-4 h-4' : 'w-3.5 h-3.5'} flex-shrink-0 ${isActive(item.url) ? 'text-sidebar-primary-foreground' : item.color} transition-all duration-300`} />
                      </div>
                      {!collapsed && (
                        <div className="flex flex-col transition-all duration-300 ease-in-out opacity-100 animate-in fade-in slide-in-from-left-2">
                          <span className="text-sm font-medium transition-all duration-300 ease-in-out">{item.title}</span>

                        </div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Colorful Footer Section */}
        {!collapsed && (
          <div className="mx-3 mt-4 glass-card hover:shadow-elevated transition-all duration-300 p-4 rounded-xl bg-sidebar-accent/15 border border-sidebar-border hover:border-sidebar-primary/30">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-sidebar-primary rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-sidebar-primary">Market Status</span>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-success to-chart-4 rounded-lg mx-auto mb-1 flex items-center justify-center">
                    <div className="w-2 h-2 bg-sidebar-foreground rounded-full"></div>
                  </div>
                  <span className="text-xs text-success font-medium">BTC</span>
                </div>
                <div className="text-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-sidebar-primary to-chart-1 rounded-lg mx-auto mb-1 flex items-center justify-center">
                    <div className="w-2 h-2 bg-sidebar-foreground rounded-full"></div>
                  </div>
                  <span className="text-xs text-sidebar-primary font-medium">ETH</span>
                </div>
                <div className="text-center">
                  <div className="w-6 h-6 bg-gradient-to-br from-warning to-chart-5 rounded-lg mx-auto mb-1 flex items-center justify-center">
                    <div className="w-2 h-2 bg-sidebar-foreground rounded-full"></div>
                  </div>
                  <span className="text-xs text-warning font-medium">SOL</span>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </SidebarContent>
    </Sidebar>
  );
}