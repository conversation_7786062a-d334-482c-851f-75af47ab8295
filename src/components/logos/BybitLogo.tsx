import React from 'react';

interface BybitLogoProps {
  className?: string;
  size?: number;
}

export const BybitLogo: React.FC<BybitLogoProps> = ({
  className = "",
  size = 32
}) => {
  return (
    <img
      src="https://logo.clearbit.com/bybit.com"
      alt="Bybit Logo"
      width={size}
      height={size}
      className={`rounded-lg ${className}`}
      style={{
        filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))',
        objectFit: 'contain'
      }}
      onError={(e) => {
        // Fallback to a simple SVG if the image fails to load
        const target = e.target as HTMLImageElement;
        target.style.display = 'none';
        const fallback = document.createElement('div');
        fallback.innerHTML = `
          <div style="width: ${size}px; height: ${size}px; background: #F7931A; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
            <span style="color: white; font-size: ${size * 0.4}px; font-weight: bold;">B</span>
          </div>
        `;
        target.parentNode?.appendChild(fallback.firstElementChild!);
      }}
    />
  );
};
