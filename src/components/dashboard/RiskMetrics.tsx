import { Shield, AlertTriangle, TrendingDown, Activity } from "lucide-react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

export function RiskMetrics() {
  const riskMetrics = {
    overallRisk: 35, // out of 100
    maxDrawdown: 8.5,
    sharpeRatio: 1.8,
    volatility: 12.3,
    valueAtRisk: 2847.50,
    riskLevel: "Medium"
  };

  const getRiskColor = (risk: number) => {
    if (risk <= 30) return "text-profit";
    if (risk <= 60) return "text-warning";
    return "text-destructive";
  };

  const getRiskBgColor = (risk: number) => {
    if (risk <= 30) return "bg-profit";
    if (risk <= 60) return "bg-warning";
    return "bg-destructive";
  };

  return (
    <Card className="bg-card shadow-card border-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Shield className="w-5 h-5 text-primary" />
            Risk Management
          </CardTitle>
          <Badge 
            variant="secondary" 
            className={`${getRiskColor(riskMetrics.overallRisk)} ${getRiskBgColor(riskMetrics.overallRisk)}/10 border-current/20`}
          >
            {riskMetrics.riskLevel} Risk
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Risk Score */}
        <div className="text-center space-y-3">
          <div className="relative w-24 h-24 mx-auto">
            <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="hsl(var(--secondary))"
                strokeWidth="8"
                fill="none"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke={`hsl(var(--${riskMetrics.overallRisk <= 30 ? 'profit' : riskMetrics.overallRisk <= 60 ? 'warning' : 'destructive'}))`}
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${riskMetrics.overallRisk * 2.51} 251`}
                strokeLinecap="round"
                className="transition-all duration-1000 ease-out"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`text-xl font-bold ${getRiskColor(riskMetrics.overallRisk)}`}>
                {riskMetrics.overallRisk}
              </span>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">Overall Risk Score</div>
        </div>

        {/* Risk Metrics Grid */}
        <div className="space-y-4">
          {/* Max Drawdown */}
          <div className="bg-secondary/50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-foreground">Max Drawdown</span>
              </div>
              <span className="text-sm font-medium text-foreground">
                {riskMetrics.maxDrawdown}%
              </span>
            </div>
            <Progress value={riskMetrics.maxDrawdown} max={20} className="h-2" />
          </div>

          {/* Sharpe Ratio */}
          <div className="bg-secondary/50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-foreground">Sharpe Ratio</span>
              </div>
              <span className="text-sm font-medium text-profit">
                {riskMetrics.sharpeRatio}
              </span>
            </div>
            <Progress value={riskMetrics.sharpeRatio * 20} max={100} className="h-2" />
          </div>

          {/* Volatility */}
          <div className="bg-secondary/50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-foreground">Volatility (30d)</span>
              </div>
              <span className="text-sm font-medium text-foreground">
                {riskMetrics.volatility}%
              </span>
            </div>
            <Progress value={riskMetrics.volatility} max={50} className="h-2" />
          </div>

          {/* Value at Risk */}
          <div className="bg-secondary/50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-foreground">Value at Risk (95%)</span>
              </div>
              <span className="text-sm font-medium text-destructive">
                ${riskMetrics.valueAtRisk.toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* Risk Recommendations */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-foreground">Recommendations</h4>
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground bg-secondary/30 rounded p-2">
              ✓ Portfolio diversification is adequate
            </div>
            <div className="text-xs text-muted-foreground bg-secondary/30 rounded p-2">
              ⚠ Consider reducing leverage on high-volatility pairs
            </div>
            <div className="text-xs text-muted-foreground bg-secondary/30 rounded p-2">
              ⚠ Review stop-loss levels for better risk control
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}