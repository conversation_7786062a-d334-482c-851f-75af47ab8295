import { TrendingUp, TrendingDown, Star } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function TopPerformers() {
  const topPerformers = [
    {
      symbol: "BTC",
      name: "Bitcoin",
      price: 45120.50,
      change: 4.32,
      changeAmount: 1870.25,
      volume: "2.8B",
      marketCap: "885B",
      image: "₿"
    },
    {
      symbol: "ETH",
      name: "Ethereum",
      price: 2890.75,
      change: 7.84,
      changeAmount: 210.30,
      volume: "1.2B",
      marketCap: "347B",
      image: "Ξ"
    },
    {
      symbol: "ADA",
      name: "Card<PERSON>",
      price: 0.485,
      change: 12.45,
      changeAmount: 0.054,
      volume: "580M",
      marketCap: "17.2B",
      image: "₳"
    },
    {
      symbol: "SOL",
      name: "Sol<PERSON>",
      price: 102.35,
      change: -2.15,
      changeAmount: -2.25,
      volume: "890M",
      marketCap: "47.8B",
      image: "◎"
    },
    {
      symbol: "DOT",
      name: "<PERSON><PERSON><PERSON>",
      price: 6.84,
      change: 8.92,
      changeAmount: 0.56,
      volume: "320M",
      marketCap: "9.1B",
      image: "●"
    }
  ];

  return (
    <Card className="bg-card shadow-card border-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Star className="w-5 h-5 text-primary" />
            Top Performers
          </CardTitle>
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            24h
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {topPerformers.map((crypto, index) => (
          <div key={crypto.symbol} className="bg-secondary/50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              {/* Crypto Info */}
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold">
                  {crypto.image}
                </div>
                <div>
                  <div className="font-medium text-foreground">
                    {crypto.symbol}
                    <span className="text-sm text-muted-foreground ml-2">{crypto.name}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Vol: {crypto.volume} • MCap: {crypto.marketCap}
                  </div>
                </div>
              </div>

              {/* Price & Change */}
              <div className="text-right">
                <div className="font-semibold text-foreground">
                  ${crypto.price.toLocaleString('en-US', { 
                    minimumFractionDigits: crypto.price < 1 ? 3 : 2,
                    maximumFractionDigits: crypto.price < 1 ? 3 : 2
                  })}
                </div>
                <div className={`flex items-center gap-1 text-sm ${crypto.change > 0 ? 'text-profit' : 'text-loss'}`}>
                  {crypto.change > 0 ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  <span>
                    {crypto.change > 0 ? '+' : ''}{crypto.change}%
                  </span>
                </div>
              </div>
            </div>

            {/* Performance Bar */}
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Performance</span>
                <span>{crypto.change > 0 ? '+' : ''}${crypto.changeAmount.toLocaleString()}</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-1.5">
                <div 
                  className={`h-1.5 rounded-full transition-all duration-500 ${
                    crypto.change > 0 ? 'bg-profit' : 'bg-loss'
                  }`}
                  style={{ 
                    width: `${Math.min(Math.abs(crypto.change) * 5, 100)}%` 
                  }}
                />
              </div>
            </div>
          </div>
        ))}

        {/* Market Summary */}
        <div className="bg-gradient-primary rounded-lg p-4 text-white mt-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-white/80">Market Cap</div>
              <div className="text-lg font-semibold">$1.23T</div>
            </div>
            <div>
              <div className="text-sm text-white/80">24h Volume</div>
              <div className="text-lg font-semibold">$89.4B</div>
            </div>
            <div>
              <div className="text-sm text-white/80">BTC Dominance</div>
              <div className="text-lg font-semibold">52.3%</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}