import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  Bot, 
  Play, 
  Pause, 
  Settings, 
  TrendingUp, 
  TrendingDown,
  Activity,
  DollarSign,
  Zap
} from 'lucide-react';

interface TradingBot {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped' | 'error';
  strategy: string;
  profit: number;
  profitPercentage: number;
  trades: number;
  winRate: number;
  lastTrade?: string;
  riskLevel: 'low' | 'medium' | 'high';
}

interface TradingBotCardProps {
  bot: TradingBot;
  onToggle?: (botId: string) => void;
  onSettings?: (botId: string) => void;
  className?: string;
  loading?: boolean;
}

export function TradingBotCard({
  bot,
  onToggle,
  onSettings,
  className,
  loading = false,
}: TradingBotCardProps) {
  const getStatusColor = (status: TradingBot['status']) => {
    switch (status) {
      case 'active':
        return 'bg-success text-success-foreground';
      case 'paused':
        return 'bg-warning text-warning-foreground';
      case 'stopped':
        return 'bg-muted text-muted-foreground';
      case 'error':
        return 'bg-destructive text-destructive-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getRiskColor = (risk: TradingBot['riskLevel']) => {
    switch (risk) {
      case 'low':
        return 'text-success border-success/30 bg-success/10';
      case 'medium':
        return 'text-warning border-warning/30 bg-warning/10';
      case 'high':
        return 'text-destructive border-destructive/30 bg-destructive/10';
      default:
        return 'text-muted-foreground border-border bg-muted/10';
    }
  };

  const getStatusIcon = (status: TradingBot['status']) => {
    switch (status) {
      case 'active':
        return <Activity className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'stopped':
        return <Bot className="h-4 w-4" />;
      case 'error':
        return <Zap className="h-4 w-4" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card className={cn('glass-card', className)}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 w-32 bg-muted/30 rounded animate-pulse" />
              <div className="h-4 w-24 bg-muted/30 rounded animate-pulse" />
            </div>
            <div className="h-6 w-16 bg-muted/30 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-1">
                <div className="h-3 w-16 bg-muted/30 rounded animate-pulse" />
                <div className="h-4 w-20 bg-muted/30 rounded animate-pulse" />
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <div className="h-8 w-20 bg-muted/30 rounded animate-pulse" />
            <div className="h-8 w-8 bg-muted/30 rounded animate-pulse" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300', className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Bot className="h-5 w-5 text-primary" />
              {bot.name}
            </CardTitle>
            <p className="text-sm text-muted-foreground">{bot.strategy}</p>
          </div>
          <Badge className={cn('flex items-center gap-1', getStatusColor(bot.status))}>
            {getStatusIcon(bot.status)}
            {bot.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Performance Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Profit</p>
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4 text-success" />
              <span className={cn(
                'font-semibold',
                bot.profit >= 0 ? 'text-success' : 'text-destructive'
              )}>
                ${Math.abs(bot.profit).toLocaleString()}
              </span>
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Return</p>
            <div className="flex items-center gap-1">
              {bot.profitPercentage >= 0 ? (
                <TrendingUp className="h-4 w-4 text-success" />
              ) : (
                <TrendingDown className="h-4 w-4 text-destructive" />
              )}
              <span className={cn(
                'font-semibold',
                bot.profitPercentage >= 0 ? 'text-success' : 'text-destructive'
              )}>
                {bot.profitPercentage > 0 ? '+' : ''}{bot.profitPercentage}%
              </span>
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Trades</p>
            <p className="font-semibold text-foreground">{bot.trades}</p>
          </div>

          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Win Rate</p>
            <p className="font-semibold text-foreground">{bot.winRate}%</p>
          </div>
        </div>

        {/* Risk Level */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Risk Level</span>
          <Badge variant="outline" className={cn('text-xs', getRiskColor(bot.riskLevel))}>
            {bot.riskLevel.toUpperCase()}
          </Badge>
        </div>

        {/* Last Trade */}
        {bot.lastTrade && (
          <div className="text-xs text-muted-foreground">
            Last trade: {bot.lastTrade}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            variant={bot.status === 'active' ? 'outline' : 'default'}
            onClick={() => onToggle?.(bot.id)}
            className="flex-1"
          >
            {bot.status === 'active' ? (
              <>
                <Pause className="h-4 w-4 mr-1" />
                Pause
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                Start
              </>
            )}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onSettings?.(bot.id)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
