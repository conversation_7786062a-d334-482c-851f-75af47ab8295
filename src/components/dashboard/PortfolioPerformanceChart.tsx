import React, { useState, useEffect, useMemo } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { useDashboardData } from '@/hooks/useDashboardData';
import { PortfolioPerformancePoint } from '@/services/dashboardApi';
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';

interface PortfolioPerformanceChartProps {
  className?: string;
  height?: number;
}

interface ChartDataPoint {
  name: string;
  value: number;
  timestamp: number;
  pnl: number;
  pnl_percent: number;
  date: string;
  time: string;
}

const TIME_PERIODS = [
  { key: '1D', label: '24H', value: '1D' },
  { key: '1W', label: '7D', value: '1W' },
  { key: '1M', label: '1M', value: '1M' },
  { key: '3M', label: '3M', value: '3M' },
  { key: '6M', label: '6M', value: '6M' },
  { key: '1Y', label: '1Y', value: '1Y' },
];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload as ChartDataPoint;
    return (
      <div className="glass-card border border-card-border/60 backdrop-blur-md p-3 rounded-lg shadow-elevated">
        <div className="space-y-2">
          <p className="text-sm font-medium text-foreground">{data.date}</p>
          <p className="text-xs text-muted-foreground">{data.time}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-muted-foreground">Portfolio Value: </span>
              <span className="font-semibold text-foreground">${data.value.toLocaleString()}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">P&L: </span>
              <span className={`font-semibold ${data.pnl >= 0 ? 'text-success' : 'text-destructive'}`}>
                {data.pnl >= 0 ? '+' : ''}${data.pnl.toFixed(2)} ({data.pnl_percent >= 0 ? '+' : ''}{data.pnl_percent.toFixed(2)}%)
              </span>
            </p>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

export function PortfolioPerformanceChart({ className, height = 400 }: PortfolioPerformanceChartProps) {
  const [selectedPeriod, setSelectedPeriod] = useState('1M');
  const { portfolioPerformance, loading, refreshPortfolioPerformance } = useDashboardData();

  // Refresh data when period changes
  useEffect(() => {
    refreshPortfolioPerformance(selectedPeriod);
  }, [selectedPeriod, refreshPortfolioPerformance]);

  // Transform API data to chart format
  const chartData = useMemo(() => {
    if (!portfolioPerformance?.data_points || portfolioPerformance.data_points.length === 0) {
      return [];
    }

    return portfolioPerformance.data_points.map((point: PortfolioPerformancePoint) => {
      const date = new Date(point.timestamp * 1000);
      
      // Format based on time period
      let name: string;
      let dateStr: string;
      let timeStr: string;

      if (selectedPeriod === '1D') {
        name = date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
        dateStr = date.toLocaleDateString('en-US', { 
          weekday: 'long', 
          month: 'short', 
          day: 'numeric' 
        });
        timeStr = date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          second: '2-digit'
        });
      } else if (selectedPeriod === '1W') {
        name = date.toLocaleDateString('en-US', { weekday: 'short', day: 'numeric' });
        dateStr = date.toLocaleDateString('en-US', { 
          weekday: 'long', 
          month: 'long', 
          day: 'numeric' 
        });
        timeStr = date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit'
        });
      } else {
        name = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        dateStr = date.toLocaleDateString('en-US', { 
          weekday: 'long', 
          month: 'long', 
          day: 'numeric',
          year: 'numeric'
        });
        timeStr = date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit'
        });
      }

      return {
        name,
        value: point.value_usd,
        timestamp: point.timestamp,
        pnl: point.pnl,
        pnl_percent: point.pnl_percent,
        date: dateStr,
        time: timeStr,
      };
    });
  }, [portfolioPerformance, selectedPeriod]);



  // Calculate performance metrics and chart domain
  const performanceMetrics = useMemo(() => {
    if (!portfolioPerformance) return null;

    const totalReturn = portfolioPerformance.total_return;
    const totalReturnPercent = portfolioPerformance.total_return_percent;
    const isPositive = totalReturn >= 0;

    return {
      totalReturn,
      totalReturnPercent,
      isPositive,
    };
  }, [portfolioPerformance]);

  // Calculate Y-axis domain for better chart visibility
  const yAxisDomain = useMemo(() => {
    if (!chartData || chartData.length === 0) return ['auto', 'auto'];

    const values = chartData.map(d => d.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const padding = (maxValue - minValue) * 0.1; // 10% padding

    return [
      Math.max(0, minValue - padding),
      maxValue + padding
    ];
  }, [chartData]);

  if (loading) {
    return (
      <Card className={cn('glass-card', className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <div className="flex gap-2">
              {TIME_PERIODS.map((period) => (
                <Skeleton key={period.key} className="h-8 w-12" />
              ))}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full" style={{ height }} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300', className)}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg font-semibold text-foreground">
                Portfolio Performance
              </CardTitle>
            </div>
            {performanceMetrics && (
              <Badge 
                className={cn(
                  'flex items-center gap-1',
                  performanceMetrics.isPositive 
                    ? 'bg-success/10 text-success border-success/30' 
                    : 'bg-destructive/10 text-destructive border-destructive/30'
                )}
              >
                {performanceMetrics.isPositive ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                {performanceMetrics.isPositive ? '+' : ''}
                {performanceMetrics.totalReturnPercent.toFixed(2)}%
              </Badge>
            )}
          </div>
          
          <div className="flex gap-1">
            {TIME_PERIODS.map((period) => (
              <Button
                key={period.key}
                variant={selectedPeriod === period.value ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setSelectedPeriod(period.value)}
                className={cn(
                  'h-8 px-3 text-xs font-medium transition-all duration-200',
                  selectedPeriod === period.value
                    ? 'bg-primary text-primary-foreground shadow-glow'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                )}
              >
                {period.label}
              </Button>
            ))}
          </div>
        </div>
        
        {performanceMetrics && (
          <div className="flex items-center gap-4 mt-2 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Total Return:</span>
              <span className={cn(
                'font-semibold',
                performanceMetrics.isPositive ? 'text-success' : 'text-destructive'
              )}>
                {performanceMetrics.isPositive ? '+' : ''}${performanceMetrics.totalReturn.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Period:</span>
              <span className="font-medium text-foreground">{selectedPeriod}</span>
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        {chartData.length === 0 ? (
          <div className="flex items-center justify-center h-96 text-muted-foreground">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No portfolio performance data available</p>
              <p className="text-xs mt-1">Data will appear once portfolio history is available</p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
            <defs>
              <linearGradient id="portfolioGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity={0.4} />
                <stop offset="50%" stopColor="hsl(var(--primary))" stopOpacity={0.2} />
                <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity={0.05} />
              </linearGradient>
            </defs>
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="hsl(var(--border))" 
              opacity={0.3}
            />
            <XAxis 
              dataKey="name"
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              domain={yAxisDomain}
              tickFormatter={(value) => {
                if (value >= 1000000) {
                  return `$${(value / 1000000).toFixed(1)}M`;
                } else if (value >= 1000) {
                  return `$${(value / 1000).toFixed(1)}k`;
                } else {
                  return `$${value.toFixed(0)}`;
                }
              }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="value"
              stroke="hsl(var(--primary))"
              strokeWidth={3}
              fill="url(#portfolioGradient)"
              fillOpacity={0.6}
              dot={false}
              activeDot={{
                r: 6,
                stroke: "hsl(var(--primary))",
                strokeWidth: 3,
                fill: "hsl(var(--background))",
                shadowBlur: 10,
              }}
            />
          </AreaChart>
        </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
}
