import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AssetAllocationData {
  name: string;
  value: number;
  amount: number;
  changePercent?: number;
}

interface AssetAllocationProps {
  title?: string;
  data: AssetAllocationData[];
  height?: number;
  className?: string;
  loading?: boolean;
  showLegend?: boolean;
}

const chartColors = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(var(--chart-6))',
];

const LoadingSkeleton = ({ height }: { height: number }) => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted/30 rounded animate-pulse" />
    <div 
      className="bg-muted/20 rounded-lg animate-pulse" 
      style={{ height: `${height}px` }}
    />
    <div className="space-y-2">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="flex items-center gap-3">
          <div className="w-4 h-4 bg-muted/30 rounded animate-pulse" />
          <div className="h-4 w-20 bg-muted/30 rounded animate-pulse" />
          <div className="h-4 w-16 bg-muted/30 rounded animate-pulse ml-auto" />
        </div>
      ))}
    </div>
  </div>
);

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: {
      name: string;
      value: number;
      percentage: number;
      color: string;
    };
  }>;
}

const CustomTooltip = ({ active, payload }: TooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="glass-card p-3 border border-card-border/60 backdrop-blur-md shadow-lg">
        <p className="text-sm font-semibold text-foreground mb-1">{data.name}</p>
        <p className="text-sm text-muted-foreground">
          Allocation: <span className="text-foreground font-medium">{data.value.toFixed(5)}%</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Value: <span className="text-foreground font-medium">${data.amount.toLocaleString()}</span>
        </p>
        {data.changePercent !== undefined && (
          <p className="text-sm text-muted-foreground">
            24h Change: <span className={cn(
              'font-medium',
              data.changePercent >= 0 ? 'text-success' : 'text-destructive'
            )}>
              {data.changePercent > 0 ? '+' : ''}{data.changePercent.toFixed(5)}%
            </span>
          </p>
        )}
      </div>
    );
  }
  return null;
};

interface LabelProps {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
}

const renderCustomLabel = (props: LabelProps) => {
  const { cx, cy, midAngle, innerRadius, outerRadius, percent } = props;
  
  // Only show labels for segments > 5%
  if (percent < 0.05) return null;
  
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={10}
      fontWeight="600"
      style={{
        filter: 'drop-shadow(1px 1px 2px rgba(0,0,0,0.8))',
      }}
    >
      {`${(percent * 100).toFixed(5)}%`}
    </text>
  );
};

export function AssetAllocation({
  title = "Asset Allocation",
  data,
  height = 400,
  className,
  loading = false,
  showLegend = true,
}: AssetAllocationProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  if (loading) {
    return (
      <Card className={cn('glass-card', className)}>
        <CardHeader>
          <LoadingSkeleton height={height} />
        </CardHeader>
      </Card>
    );
  }

  const totalValue = data.reduce((sum, item) => sum + item.amount, 0);
  // Increased pie chart diameter by 30% from reduced size (98 * 1.3 = 127.4)
  const outerRadius = Math.min(height * 0.32, 127); // 98 * 1.3 ≈ 127
  const innerRadius = outerRadius * 0.6;

  return (
    <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300', className)}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-foreground">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className={cn("space-y-3", !showLegend && "space-y-0")}>
          {/* Pie Chart */}
          <div className="flex justify-center">
            <ResponsiveContainer width="100%" height={Math.max(height * 0.7, 140)}>
              <PieChart>
                <defs>
                  {chartColors.map((color, index) => (
                    <linearGradient key={index} id={`gradient-${index}`} x1="0" y1="0" x2="1" y2="1">
                      <stop offset="0%" stopColor={color} stopOpacity={1} />
                      <stop offset="100%" stopColor={color} stopOpacity={0.7} />
                    </linearGradient>
                  ))}
                </defs>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={outerRadius}
                  innerRadius={innerRadius}
                  fill="#8884d8"
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={800}
                  onMouseEnter={(_, index) => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {data.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#gradient-${index % chartColors.length})`}
                      stroke={hoveredIndex === index ? chartColors[index % chartColors.length] : 'transparent'}
                      strokeWidth={hoveredIndex === index ? 3 : 0}
                      style={{
                        filter: hoveredIndex === index ? 'brightness(1.1)' : 'brightness(1)',
                        transition: 'all 0.3s ease',
                      }}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Legend - conditionally rendered */}
          {showLegend && (
            <div className="space-y-2">
              {data.map((item, index) => {
                const percentage = ((item.amount / totalValue) * 100).toFixed(5);
                const isHovered = hoveredIndex === index;

                return (
                  <div
                    key={item.name}
                    className={cn(
                      'flex items-center justify-between p-2 rounded-lg transition-all duration-300 cursor-pointer',
                      'hover:bg-card-glass/50 hover:shadow-sm',
                      isHovered && 'bg-card-glass/50 shadow-sm'
                    )}
                    onMouseEnter={() => setHoveredIndex(index)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full border border-background shadow-sm"
                        style={{ backgroundColor: chartColors[index % chartColors.length] }}
                      />
                      <div>
                        <p className="text-xs font-medium text-foreground">{item.name}</p>
                        <p className="text-xs text-muted-foreground">{percentage}%</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <p className="text-xs font-semibold text-foreground">
                        ${item.amount.toLocaleString()}
                      </p>
                      {item.changePercent !== undefined && (
                        <div className={cn(
                          'flex items-center gap-1 text-xs',
                          item.changePercent >= 0 ? 'text-success' : 'text-destructive'
                        )}>
                          {item.changePercent >= 0 ? (
                            <TrendingUp className="w-2 h-2" />
                          ) : (
                            <TrendingDown className="w-2 h-2" />
                          )}
                          {item.changePercent > 0 ? '+' : ''}{item.changePercent.toFixed(1)}%
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
