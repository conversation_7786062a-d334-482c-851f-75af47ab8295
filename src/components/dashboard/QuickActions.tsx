import { Play, Pause, Setting<PERSON>, <PERSON><PERSON><PERSON>3, Plus, RefreshCw } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function QuickActions() {
  return (
    <Card className="relative overflow-hidden group hover:shadow-elevated transition-all duration-500">
      <div className="absolute inset-0 bg-gradient-primary opacity-5 group-hover:opacity-10 transition-opacity duration-500"></div>
      <CardHeader className="pb-3 relative z-10">
        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
          <div className="w-2 h-2 bg-gradient-primary rounded-full animate-pulse-glow"></div>
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-5 relative z-10">
        {/* Bot Controls */}
        <div className="space-y-3 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
            <div className="w-1 h-4 bg-gradient-primary rounded-full"></div>
            Bot Controls
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <Button 
              size="sm" 
              variant="premium"
              className="flex items-center gap-2 shadow-glow hover:scale-105 transition-transform duration-300"
            >
              <Play className="w-4 h-4" />
              Start All
            </Button>
            <Button 
              variant="destructive" 
              size="sm" 
              className="flex items-center gap-2 shadow-button"
            >
              <Pause className="w-4 h-4" />
              Pause All
            </Button>
          </div>
        </div>

        {/* Strategy Management */}
        <div className="space-y-3 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
            <div className="w-1 h-4 bg-gradient-primary rounded-full"></div>
            Strategy
          </h4>
          <div className="space-y-3">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start hover:bg-gradient-card/50 transition-all duration-300"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Strategy
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start hover:bg-gradient-card/50 transition-all duration-300"
            >
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </Button>
          </div>
        </div>

        {/* Current Status */}
        <div className="space-y-3 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
            <div className="w-1 h-4 bg-gradient-primary rounded-full"></div>
            Current Status
          </h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
              <span className="text-sm font-medium text-foreground">Market Connection</span>
              <Badge variant="secondary" className="bg-gradient-profit text-white border-0 shadow-glow animate-pulse">
                Connected
              </Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
              <span className="text-sm font-medium text-foreground">Active Strategies</span>
              <Badge variant="secondary" className="bg-gradient-primary text-white border-0 shadow-glow">
                3 Running
              </Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
              <span className="text-sm font-medium text-foreground">Last Update</span>
              <span className="text-sm text-muted-foreground bg-secondary/30 px-2 py-1 rounded-md">2 min ago</span>
            </div>
          </div>
        </div>

        {/* Performance Quick View */}
        <div className="space-y-3 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
            <div className="w-1 h-4 bg-gradient-primary rounded-full"></div>
            Today's Performance
          </h4>
          <div className="grid grid-cols-2 gap-3 text-center">
            <div className="p-4 bg-gradient-profit rounded-xl shadow-glow">
              <div className="text-sm text-white/90 mb-1">Profit</div>
              <div className="text-xl font-bold text-white">+$1,247</div>
            </div>
            <div className="p-4 bg-gradient-card/50 rounded-xl backdrop-blur-sm">
              <div className="text-sm text-muted-foreground mb-1">Trades</div>
              <div className="text-xl font-bold text-foreground">24</div>
            </div>
          </div>
        </div>

        {/* Refresh Button */}
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full hover:bg-gradient-card/50 transition-all duration-300 group"
        >
          <RefreshCw className="w-4 h-4 mr-2 group-hover:rotate-180 transition-transform duration-500" />
          Refresh Data
        </Button>
      </CardContent>
    </Card>
  );
}