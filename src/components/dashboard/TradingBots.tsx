import { <PERSON><PERSON>, <PERSON>, Pause, Setting<PERSON>, TrendingUp, TrendingDown, Plus } from "lucide-react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

export function TradingBots() {
  const bots = [
    {
      id: 1,
      name: "Conservative Growth",
      strategy: "DCA + Grid",
      status: "active",
      profit: 1247.85,
      profitPercent: 8.3,
      trades: 156,
      successRate: 78,
      risk: "Low"
    },
    {
      id: 2,
      name: "Aggressive Scalper",
      strategy: "Momentum + RSI",
      status: "active",
      profit: 2156.42,
      profitPercent: 15.7,
      trades: 324,
      successRate: 65,
      risk: "High"
    },
    {
      id: 3,
      name: "Balanced Trader",
      strategy: "Mean Reversion",
      status: "paused",
      profit: 856.23,
      profitPercent: 5.2,
      trades: 89,
      successRate: 72,
      risk: "Medium"
    }
  ];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low": return "text-profit bg-profit/10 border-profit/20";
      case "Medium": return "text-warning bg-warning/10 border-warning/20";
      case "High": return "text-destructive bg-destructive/10 border-destructive/20";
      default: return "text-neutral bg-neutral/10 border-neutral/20";
    }
  };

  const getStatusColor = (status: string) => {
    return status === "active" 
      ? "text-profit bg-profit/10 border-profit/20" 
      : "text-muted-foreground bg-muted/10 border-muted/20";
  };

  return (
    <Card className="relative overflow-hidden group hover:shadow-elevated transition-all duration-500">
      <div className="absolute inset-0 bg-gradient-primary opacity-5 group-hover:opacity-10 transition-opacity duration-500"></div>
      <CardHeader className="pb-3 relative z-10">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-glow">
              <Bot className="w-5 h-5 text-white" />
            </div>
            AI Trading Bots
          </CardTitle>
          <Button size="sm" variant="premium" className="shadow-glow hover:scale-105 transition-transform duration-300">
            <Plus className="w-4 h-4 mr-2" />
            Add Bot
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 relative z-10">
        {bots.map((bot) => (
          <div key={bot.id} className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 space-y-4 border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            {/* Bot Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center shadow-glow group-hover:scale-110 transition-transform duration-300 relative">
                  <Bot className="w-6 h-6 text-white" />
                  {bot.status === "active" && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-profit rounded-full animate-pulse-glow"></div>
                  )}
                </div>
                <div>
                  <div className="font-bold text-foreground group-hover:text-primary transition-colors duration-300">{bot.name}</div>
                  <div className="text-sm text-muted-foreground bg-secondary/30 px-2 py-1 rounded-md inline-block">{bot.strategy}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant="secondary" 
                  className={`${bot.status === "active" ? 'bg-gradient-profit text-white shadow-glow' : 'bg-gradient-card text-foreground'} border-0`}
                >
                  {bot.status === "active" ? "Active" : "Paused"}
                </Badge>
                <Badge 
                  variant="secondary" 
                  className={`${getRiskColor(bot.risk)} border-0`}
                >
                  {bot.risk} Risk
                </Badge>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
                <div className="text-xs text-muted-foreground mb-1">Profit</div>
                <div className="flex items-center justify-center gap-1 mb-1">
                  <div className="p-1 bg-gradient-profit rounded-full">
                    <TrendingUp className="w-3 h-3 text-white" />
                  </div>
                  <span className="font-bold text-profit">
                    +${bot.profit.toLocaleString()}
                  </span>
                </div>
                <div className="text-xs text-profit font-semibold">+{bot.profitPercent}%</div>
              </div>
              <div className="text-center p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
                <div className="text-xs text-muted-foreground mb-1">Trades</div>
                <div className="font-bold text-foreground text-lg">{bot.trades}</div>
                <div className="text-xs text-muted-foreground">Total</div>
              </div>
              <div className="text-center p-3 bg-gradient-card/50 rounded-lg backdrop-blur-sm">
                <div className="text-xs text-muted-foreground mb-1">Success Rate</div>
                <div className="font-bold text-foreground text-lg">{bot.successRate}%</div>
                <div className="w-full bg-secondary/30 rounded-full h-1.5 mt-2 overflow-hidden">
                  <div 
                    className="h-full bg-gradient-profit transition-all duration-500 ease-out"
                    style={{ width: `${bot.successRate}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 pt-2 border-t border-border/30">
              {bot.status === "active" ? (
                <Button size="sm" variant="destructive" className="flex-1 shadow-button">
                  <Pause className="w-4 h-4 mr-2" />
                  Pause
                </Button>
              ) : (
                <Button size="sm" variant="premium" className="flex-1 shadow-glow">
                  <Play className="w-4 h-4 mr-2" />
                  Start
                </Button>
              )}
              <Button size="sm" variant="outline" className="aspect-square p-0 w-10">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}