import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TrendingUp, Calendar } from "lucide-react";

export function MarketChart() {
  // Sample data for the chart
  const chartData = [
    { time: '00:00', btc: 43250, eth: 2680, portfolio: 125420 },
    { time: '04:00', btc: 43580, eth: 2720, portfolio: 126150 },
    { time: '08:00', btc: 44100, eth: 2780, portfolio: 127680 },
    { time: '12:00', btc: 43890, eth: 2750, portfolio: 126890 },
    { time: '16:00', btc: 44520, eth: 2820, portfolio: 128340 },
    { time: '20:00', btc: 44780, eth: 2850, portfolio: 129120 },
    { time: '24:00', btc: 45120, eth: 2890, portfolio: 129850 },
  ];

  const timeframes = ['1H', '4H', '1D', '1W', '1M'];

  return (
    <Card className="relative overflow-hidden group hover:shadow-elevated transition-all duration-500">
      <div className="absolute inset-0 bg-gradient-primary opacity-5 group-hover:opacity-10 transition-opacity duration-500"></div>
      <CardHeader className="pb-3 relative z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
              <div className="w-2 h-2 bg-gradient-primary rounded-full animate-pulse-glow"></div>
              Market Overview
            </CardTitle>
            <Badge variant="secondary" className="bg-gradient-profit text-white border-0 shadow-glow">
              <TrendingUp className="w-3 h-3 mr-1" />
              +2.34%
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {timeframes.map((timeframe) => (
              <Button
                key={timeframe}
                size="sm"
                variant={timeframe === '1D' ? "premium" : "outline"}
                className={timeframe === '1D' ? "shadow-glow" : "hover:bg-gradient-card/50 transition-all duration-300"}
              >
                {timeframe}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className="relative z-10">
        {/* Price Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-4 h-4 bg-chart-1 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
              <span className="text-sm text-muted-foreground">Bitcoin (BTC)</span>
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">$45,120</div>
            <div className="text-sm text-profit font-semibold bg-profit/10 px-2 py-1 rounded-md inline-block">+$1,870 (+4.3%)</div>
          </div>
          
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-4 h-4 bg-chart-2 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
              <span className="text-sm text-muted-foreground">Ethereum (ETH)</span>
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">$2,890</div>
            <div className="text-sm text-profit font-semibold bg-profit/10 px-2 py-1 rounded-md inline-block">+$210 (+7.8%)</div>
          </div>
          
          <div className="bg-gradient-glass backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-glass hover:shadow-elevated transition-all duration-300 group">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-4 h-4 bg-chart-3 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
              <span className="text-sm text-muted-foreground">Portfolio Value</span>
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">$129,850</div>
            <div className="text-sm text-profit font-semibold bg-profit/10 px-2 py-1 rounded-md inline-block">+$4,430 (+3.5%)</div>
          </div>
        </div>

        {/* Chart */}
        <div className="h-80 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <defs>
                <linearGradient id="btcGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-1))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-1))" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="ethGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-2))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-2))" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="portfolioGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--chart-3))" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="hsl(var(--chart-3))" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
              <XAxis 
                dataKey="time" 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                domain={['dataMin - 1000', 'dataMax + 1000']}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'hsl(var(--popover))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '12px',
                  color: 'hsl(var(--foreground))',
                  boxShadow: 'var(--shadow-elevated)',
                  backdropFilter: 'blur(8px)'
                }}
                labelStyle={{ color: 'hsl(var(--muted-foreground))' }}
              />
              <Line 
                type="monotone" 
                dataKey="btc" 
                stroke="hsl(var(--chart-1))" 
                strokeWidth={3}
                dot={false}
                name="Bitcoin"
                fill="url(#btcGradient)"
              />
              <Line 
                type="monotone" 
                dataKey="eth" 
                stroke="hsl(var(--chart-2))" 
                strokeWidth={3}
                dot={false}
                name="Ethereum"
                fill="url(#ethGradient)"
              />
              <Line 
                type="monotone" 
                dataKey="portfolio" 
                stroke="hsl(var(--chart-3))" 
                strokeWidth={4}
                dot={false}
                name="Portfolio"
                fill="url(#portfolioGradient)"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Chart Legend */}
        <div className="flex items-center justify-center gap-8 mt-6 p-4 bg-gradient-glass backdrop-blur-sm rounded-xl border border-border/30 shadow-glass">
          <div className="flex items-center gap-2 group cursor-pointer">
            <div className="w-4 h-4 bg-chart-1 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
            <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300">Bitcoin</span>
          </div>
          <div className="flex items-center gap-2 group cursor-pointer">
            <div className="w-4 h-4 bg-chart-2 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
            <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300">Ethereum</span>
          </div>
          <div className="flex items-center gap-2 group cursor-pointer">
            <div className="w-4 h-4 bg-chart-3 rounded-full shadow-glow group-hover:scale-125 transition-transform duration-300"></div>
            <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300">Portfolio</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}