import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { agentService, type AgentStatus } from '@/services/agentService';
import { cn } from '@/lib/utils';
import { 
  Bot, 
  Play, 
  Square, 
  Settings, 
  TrendingUp, 
  TrendingDown,
  Activity,
  DollarSign,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';

interface FluxTraderBotCardProps {
  className?: string;
}

export function FluxTraderBotCard({ className }: FluxTraderBotCardProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [agent, setAgent] = useState<AgentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  const agentId = 'fluxtrader_default';

  // Load agent status
  useEffect(() => {
    const loadAgentStatus = async () => {
      try {
        setIsLoading(true);
        const agentData = await agentService.getAgentStatus(agentId);
        setAgent(agentData);
      } catch (error) {
        console.error('Failed to load FluxTrader agent status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAgentStatus();
  }, []);

  // Real-time updates - poll agent status every 3 seconds
  useEffect(() => {
    const pollAgentStatus = async () => {
      try {
        const agentData = await agentService.getAgentStatus(agentId);
        setAgent(agentData);
      } catch (error) {
        console.error('Failed to poll FluxTrader agent status:', error);
      }
    };

    const interval = setInterval(pollAgentStatus, 3000);
    return () => clearInterval(interval);
  }, []);

  // Handle start agent
  const handleStartAgent = async () => {
    try {
      setIsStarting(true);
      await agentService.startAgent(agentId);
      toast({
        title: "Success",
        description: "FluxTrader agent started successfully",
      });
      // Reload agent data
      const agentData = await agentService.getAgentStatus(agentId);
      setAgent(agentData);
    } catch (error) {
      console.error('Failed to start FluxTrader agent:', error);
      toast({
        title: "Error",
        description: "Failed to start FluxTrader agent",
        variant: "destructive"
      });
    } finally {
      setIsStarting(false);
    }
  };

  // Handle stop agent
  const handleStopAgent = async () => {
    try {
      setIsStopping(true);
      await agentService.stopAgent(agentId);
      toast({
        title: "Success",
        description: "FluxTrader agent stopped successfully",
      });
      // Reload agent data
      const agentData = await agentService.getAgentStatus(agentId);
      setAgent(agentData);
    } catch (error) {
      console.error('Failed to stop FluxTrader agent:', error);
      toast({
        title: "Error",
        description: "Failed to stop FluxTrader agent",
        variant: "destructive"
      });
    } finally {
      setIsStopping(false);
    }
  };

  // Handle navigate to dashboard
  const handleNavigateToDashboard = () => {
    navigate(`/dashboard/fluxtrader/${agentId}`);
  };

  const isRunning = agent?.status === 'running' || agent?.is_running;
  const isConnected = agent?.mcp_connected && agent?.binance_connected && agent?.groq_connected;

  const getStatusColor = () => {
    if (isRunning) return 'bg-success text-success-foreground';
    if (agent?.status === 'error') return 'bg-destructive text-destructive-foreground';
    return 'bg-muted text-muted-foreground';
  };

  const getStatusIcon = () => {
    if (isRunning) return <Activity className="h-3 w-3" />;
    if (agent?.status === 'error') return <AlertTriangle className="h-3 w-3" />;
    return <Clock className="h-3 w-3" />;
  };

  if (isLoading) {
    return (
      <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300', className)}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="h-5 w-32 bg-muted animate-pulse rounded" />
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
            </div>
            <div className="h-6 w-16 bg-muted animate-pulse rounded" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 w-full bg-muted animate-pulse rounded" />
            <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('glass-card hover:shadow-elevated transition-all duration-300 cursor-pointer', className)}>
      <CardHeader className="pb-4" onClick={handleNavigateToDashboard}>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2 hover:text-primary transition-colors">
              <Bot className="h-5 w-5 text-primary" />
              FluxTrader AI Agent
            </CardTitle>
            <p className="text-sm text-muted-foreground">Pump & Dump Detection</p>
          </div>
          <Badge className={cn('flex items-center gap-1', getStatusColor())}>
            {getStatusIcon()}
            {isRunning ? 'Running' : agent?.status || 'Stopped'}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Performance Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Current Cycle</p>
            <p className="text-lg font-semibold text-foreground">
              {agent?.current_cycle || 0}/{agent?.max_cycles || 100}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">Uptime</p>
            <p className="text-lg font-semibold text-foreground">
              {agent?.uptime_seconds ? `${Math.floor(agent.uptime_seconds / 60)}m` : '0m'}
            </p>
          </div>
        </div>

        {/* Connection Status */}
        <div className="flex items-center gap-4 text-xs">
          <div className="flex items-center gap-1">
            {isConnected ? (
              <Wifi className="h-3 w-3 text-success" />
            ) : (
              <WifiOff className="h-3 w-3 text-muted-foreground" />
            )}
            <span className={isConnected ? 'text-success' : 'text-muted-foreground'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3 text-primary" />
            <span className="text-muted-foreground">Ultra-Aggressive</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            variant={isRunning ? 'outline' : 'default'}
            onClick={isRunning ? handleStopAgent : handleStartAgent}
            disabled={isStarting || isStopping}
            className="flex-1"
          >
            {isRunning ? (
              <>
                <Square className="h-4 w-4 mr-1" />
                {isStopping ? 'Stopping...' : 'Stop'}
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                {isStarting ? 'Starting...' : 'Start'}
              </>
            )}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={handleNavigateToDashboard}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
