import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Lightbulb, Target } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export function AIInsights() {
  const insights = [
    {
      type: "opportunity",
      title: "BTC Momentum Building",
      description: "AI detected strong bullish momentum in Bitcoin. Consider increasing allocation.",
      confidence: 85,
      action: "Increase Position",
      priority: "high"
    },
    {
      type: "warning",
      title: "ETH Volatility Alert",
      description: "Ethereum showing increased volatility. Risk management recommended.",
      confidence: 78,
      action: "Adjust Risk",
      priority: "medium"
    },
    {
      type: "suggestion",
      title: "DCA Strategy Optimization",
      description: "Your DCA intervals could be optimized for 12% better performance.",
      confidence: 92,
      action: "Optimize",
      priority: "low"
    }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case "opportunity": return <TrendingUp className="w-5 h-5 text-profit" />;
      case "warning": return <AlertTriangle className="w-5 h-5 text-warning" />;
      case "suggestion": return <Lightbulb className="w-5 h-5 text-primary" />;
      default: return <Brain className="w-5 h-5 text-primary" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-destructive bg-destructive/10 border-destructive/20";
      case "medium": return "text-warning bg-warning/10 border-warning/20";
      case "low": return "text-profit bg-profit/10 border-profit/20";
      default: return "text-primary bg-primary/10 border-primary/20";
    }
  };

  return (
    <Card className="bg-card shadow-card border-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            AI Insights
          </CardTitle>
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            3 Active
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight, index) => (
          <div key={index} className="bg-secondary/50 rounded-lg p-4 space-y-3">
            {/* Insight Header */}
            <div className="flex items-start justify-between gap-3">
              <div className="flex items-start gap-3">
                {getIcon(insight.type)}
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-foreground">{insight.title}</h4>
                    <Badge variant="secondary" className={getPriorityColor(insight.priority)}>
                      {insight.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {insight.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Confidence and Action */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Confidence:</span>
                <span className="text-sm font-medium text-foreground">{insight.confidence}%</span>
                <div className="w-16 h-2 bg-secondary rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary rounded-full transition-all duration-300"
                    style={{ width: `${insight.confidence}%` }}
                  />
                </div>
              </div>
              <Button size="sm" variant="outline" className="border-border text-foreground">
                {insight.action}
              </Button>
            </div>
          </div>
        ))}

        {/* AI Summary */}
        <div className="bg-gradient-primary rounded-lg p-4 text-white">
          <div className="flex items-center gap-2 mb-2">
            <Brain className="w-5 h-5" />
            <h4 className="font-medium">AI Summary</h4>
          </div>
          <p className="text-sm text-white/90">
            Market conditions favor conservative strategies. Consider reducing risk exposure 
            by 15% and implementing tighter stop-losses for optimal performance.
          </p>
          <Button size="sm" variant="secondary" className="mt-3 bg-white/20 hover:bg-white/30 text-white border-white/20">
            View Full Analysis
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}