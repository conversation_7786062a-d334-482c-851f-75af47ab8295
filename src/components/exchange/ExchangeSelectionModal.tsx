import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ExchangeCard } from './ExchangeCard';
import { BinanceConnectionFormModal } from './BinanceConnectionFormModal';
import { useExchange } from '@/contexts/ExchangeContext';
import { SUPPORTED_EXCHANGES } from '@/lib/exchangeUtils';
import { ExchangeCredentials } from '@/types/exchange';
import { ArrowLeft, Zap, TrendingUp, Shield, TestTube, DollarSign, AlertTriangle, ExternalLink } from 'lucide-react';
import { TradingEnvironment } from '@/types/exchange';

interface ExchangeSelectionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete?: () => void;
}

type ModalStep = 'selection' | 'environment' | 'binance-form';

export const ExchangeSelectionModal: React.FC<ExchangeSelectionModalProps> = ({
  open,
  onOpenChange,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState<ModalStep>('selection');
  const [selectedExchange, setSelectedExchange] = useState<string | null>(null);
  const [selectedEnvironment, setSelectedEnvironment] = useState<TradingEnvironment>('mainnet');
  const {
    exchanges,
    isConnecting,
    connectExchange,
    disconnectExchange,
    hasConnectedExchange,
    setEnvironment
  } = useExchange();

  const handleExchangeConnect = (exchangeId: string) => {
    setSelectedExchange(exchangeId);
    if (exchangeId === 'binance') {
      setCurrentStep('binance-form');
    } else {
      // For other exchanges, show coming soon message
      console.log(`${exchangeId} coming soon`);
    }
  };

  const handleEnvironmentSelect = (environment: TradingEnvironment) => {
    setSelectedEnvironment(environment);
    setEnvironment(environment, selectedExchange || undefined);
    if (selectedExchange === 'binance') {
      setCurrentStep('binance-form');
    }
  };

  const handleBinanceSubmit = async (credentials: ExchangeCredentials): Promise<boolean> => {
    console.log('🔄 [MODAL] handleBinanceSubmit called with credentials:', {
      apiKey: credentials.apiKey?.substring(0, 8) + '...',
      secretKey: credentials.secretKey?.substring(0, 8) + '...',
      testnet: false
    });

    try {
      console.log('📞 [MODAL] Calling connectExchange...');
      const success = await connectExchange('binance', credentials);
      console.log('📋 [MODAL] connectExchange result:', success);

      if (success) {
        console.log('✅ [MODAL] Connection successful, proceeding to next step...');
        // Connection successful, show success briefly then proceed
        console.log('🔄 [MODAL] Closing modal and calling onComplete...');

        // Small delay to show success state, then close modal and proceed
        setTimeout(() => {
          onOpenChange(false);
          setCurrentStep('selection');

          // Call onComplete callback to proceed to next step
          if (onComplete) {
            console.log('📞 [MODAL] Calling onComplete callback...');
            onComplete();
          } else {
            console.log('⚠️ [MODAL] No onComplete callback provided');
          }
        }, 1500); // 1.5 second delay to show success
      } else {
        console.log('❌ [MODAL] Connection failed');
      }
      return success;
    } catch (error) {
      console.error('💥 [MODAL] Error in handleBinanceSubmit:', error);
      return false;
    }
  };

  const handleBinanceCancel = () => {
    setCurrentStep('selection');
  };

  const handleSkip = () => {
    onOpenChange(false);
    setCurrentStep('selection');
  };

  const handleClose = () => {
    onOpenChange(false);
    setCurrentStep('selection');
  };

  const getStepProgress = () => {
    switch (currentStep) {
      case 'selection':
        return 50;
      case 'binance-form':
        return 100;
      default:
        return 0;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <DialogTitle className="text-xl font-semibold">
                {currentStep === 'selection' ? 'Connect Exchange' : 'Binance Setup'}
              </DialogTitle>
              <DialogDescription>
                {currentStep === 'selection' && 'Choose an exchange to start trading'}
                {currentStep === 'binance-form' && `Enter your API credentials`}
              </DialogDescription>
            </div>
            
            {currentStep === 'binance-form' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentStep('selection')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
            )}
          </div>

        </DialogHeader>

        <div className="space-y-6">
          {currentStep === 'selection' && (
            <>
              {/* Benefits Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 rounded-lg bg-gradient-to-r from-primary/5 to-accent-teal/5 border border-primary/10">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Real-time Data</h4>
                    <p className="text-xs text-muted-foreground">Live market updates</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-success/10">
                    <TrendingUp className="h-5 w-5 text-success" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Automated Trading</h4>
                    <p className="text-xs text-muted-foreground">AI-powered strategies</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-warning/10">
                    <Shield className="h-5 w-5 text-warning" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Secure Connection</h4>
                    <p className="text-xs text-muted-foreground">Encrypted credentials</p>
                  </div>
                </div>
              </div>

              {/* Exchange Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {SUPPORTED_EXCHANGES.map((exchangeInfo) => (
                  <ExchangeCard
                    key={exchangeInfo.id}
                    exchangeInfo={exchangeInfo}
                    connection={exchanges[exchangeInfo.id]}
                    onConnect={() => handleExchangeConnect(exchangeInfo.id)}
                    onDisconnect={() => disconnectExchange(exchangeInfo.id)}
                    isConnecting={isConnecting}
                  />
                ))}
              </div>

              {/* Skip Option */}
              <div className="flex justify-between items-center pt-4 border-t border-border/50">
                <p className="text-sm text-muted-foreground">
                  You can connect an exchange later in Settings
                </p>
                <div className="flex gap-3">
                  <Button variant="outline" onClick={handleSkip}>
                    Skip for Now
                  </Button>
                  {hasConnectedExchange() && (
                    <Button onClick={handleClose}>
                      Continue
                    </Button>
                  )}
                </div>
              </div>
            </>
          )}


          {currentStep === 'binance-form' && (
            <>

              <BinanceConnectionFormModal
                onSubmit={handleBinanceSubmit}
                onCancel={handleBinanceCancel}
                isConnecting={isConnecting}
                environment="mainnet"
              />
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};