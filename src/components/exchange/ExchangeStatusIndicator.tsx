import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useExchange } from '@/contexts/ExchangeContext';
import { getExchangeInfo, formatConnectionStatus, getExchangeLogoComponent } from '@/lib/exchangeUtils';
import { 
  Wifi, 
  WifiOff, 
  Settings, 
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface ExchangeStatusIndicatorProps {
  onOpenSettings?: () => void;
}

export const ExchangeStatusIndicator: React.FC<ExchangeStatusIndicatorProps> = ({
  onOpenSettings
}) => {
  const { 
    exchanges, 
    selectedExchange, 
    hasConnectedExchange,
    setShowConnectionModal,
    disconnectExchange
  } = useExchange();

  const connectedExchanges = Object.values(exchanges).filter(
    exchange => exchange.status === 'connected'
  );

  const getStatusIcon = () => {
    if (!hasConnectedExchange()) {
      return <WifiOff className="h-4 w-4 text-muted-foreground" />;
    }

    const hasError = Object.values(exchanges).some(exchange => exchange.status === 'error');
    const isConnecting = Object.values(exchanges).some(exchange => exchange.status === 'connecting');

    if (hasError) {
      return <AlertCircle className="h-4 w-4 text-destructive" />;
    }
    if (isConnecting) {
      return <Clock className="h-4 w-4 text-warning animate-spin" />;
    }
    return <CheckCircle className="h-4 w-4 text-success" />;
  };

  const getStatusText = () => {
    if (!hasConnectedExchange()) {
      return 'No Exchange';
    }

    if (connectedExchanges.length === 1) {
      const exchange = connectedExchanges[0];
      const info = getExchangeInfo(exchange.id);
      return info?.displayName || exchange.name;
    }

    return `${connectedExchanges.length} Connected`;
  };

  const getStatusColor = () => {
    if (!hasConnectedExchange()) {
      return 'text-muted-foreground';
    }

    const hasError = Object.values(exchanges).some(exchange => exchange.status === 'error');
    if (hasError) {
      return 'text-destructive';
    }

    return 'text-success';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className="flex items-center gap-2 h-8 px-3"
        >
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Wifi className="h-4 w-4" />
          Exchange Connections
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {!hasConnectedExchange() ? (
          <div className="p-3 text-center">
            <WifiOff className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-3">
              No exchanges connected
            </p>
            <Button 
              size="sm" 
              className="w-full"
              onClick={() => setShowConnectionModal(true)}
            >
              Connect Exchange
            </Button>
          </div>
        ) : (
          <>
            {/* Connected Exchanges */}
            {Object.values(exchanges).map((exchange) => {
              if (exchange.status === 'disconnected') return null;
              
              const info = getExchangeInfo(exchange.id);
              const statusInfo = formatConnectionStatus(exchange.status);
              const LogoComponent = info?.logo ? getExchangeLogoComponent(info.logo) : null;

              return (
                <DropdownMenuItem key={exchange.id} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      {LogoComponent ? (
                        <LogoComponent size={28} className="drop-shadow-sm" />
                      ) : (
                        <div className="w-7 h-7 rounded-lg bg-muted/20 flex items-center justify-center">
                          <span className="text-xs font-bold text-muted-foreground">
                            {info?.displayName?.charAt(0) || '?'}
                          </span>
                        </div>
                      )}
                      {exchange.status === 'connected' && (
                        <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-success rounded-full animate-pulse"></div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium">{info?.displayName}</p>
                      <div className="flex items-center gap-1">
                        <div className={`w-2 h-2 rounded-full ${
                          exchange.status === 'connected' ? 'bg-success' :
                          exchange.status === 'connecting' ? 'bg-warning' :
                          'bg-destructive'
                        }`} />
                        <span className={`text-xs ${statusInfo.color}`}>
                          {statusInfo.text}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {exchange.status === 'connected' && (
                    <Badge variant="secondary" className="text-xs">
                      Active
                    </Badge>
                  )}
                </DropdownMenuItem>
              );
            })}

            <DropdownMenuSeparator />

            {/* Actions */}
            <DropdownMenuItem 
              onClick={() => setShowConnectionModal(true)}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Manage Connections
            </DropdownMenuItem>

            {onOpenSettings && (
              <DropdownMenuItem 
                onClick={onOpenSettings}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Exchange Settings
              </DropdownMenuItem>
            )}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
