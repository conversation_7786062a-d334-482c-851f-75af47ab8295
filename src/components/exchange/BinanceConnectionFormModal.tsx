import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ExchangeCredentials } from '@/types/exchange';
import {
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react';

const binanceCredentialsSchema = z.object({
  apiKey: z.string().min(1, 'API Key is required'),
  secretKey: z.string().min(1, 'Secret Key is required')
});

type BinanceCredentialsForm = z.infer<typeof binanceCredentialsSchema>;

interface BinanceConnectionFormModalProps {
  onSubmit: (credentials: ExchangeCredentials) => Promise<boolean>;
  onCancel: () => void;
  isConnecting: boolean;
  environment?: 'mainnet';
}

export const BinanceConnectionFormModal: React.FC<BinanceConnectionFormModalProps> = ({
  onSubmit,
  onCancel,
  isConnecting
}) => {
  const [showSecretKey, setShowSecretKey] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<BinanceCredentialsForm>({
    resolver: zodResolver(binanceCredentialsSchema),
    mode: 'onChange'
  });

  const handleFormSubmit = async (data: BinanceCredentialsForm) => {
    try {
      await onSubmit(data as ExchangeCredentials);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <div className="w-96 mx-auto p-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-3">Connect Binance Exchange</h2>
        <p className="text-muted-foreground text-base">
          Enter your API credentials to start automated trading
        </p>
      </div>

      {/* IP Whitelist Info */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div className="flex-1">
            <p className="font-medium text-blue-900 text-xs mb-1">Add IP to whitelist: <span className="font-mono font-semibold">*************</span></p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="space-y-3">
          <Label htmlFor="apiKey" className="text-base font-medium text-foreground">
            API Key
          </Label>
          <Input
            id="apiKey"
            type="text"
            placeholder="Enter your Binance API key (64 characters)"
            {...register('apiKey')}
            className="h-12 text-base px-4"
          />
          {errors.apiKey && (
            <p className="text-sm text-red-500 mt-2">{errors.apiKey.message}</p>
          )}
        </div>

        <div className="space-y-3">
          <Label htmlFor="secretKey" className="text-base font-medium text-foreground">
            Secret Key
          </Label>
          <div className="relative">
            <Input
              id="secretKey"
              type={showSecretKey ? "text" : "password"}
              placeholder="Enter your Binance secret key"
              {...register('secretKey')}
              className="h-12 text-base px-4 pr-12"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-12 w-12 p-0 hover:bg-transparent"
              onClick={() => setShowSecretKey(!showSecretKey)}
            >
              {showSecretKey ? (
                <EyeOff className="h-5 w-5 text-muted-foreground" />
              ) : (
                <Eye className="h-5 w-5 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.secretKey && (
            <p className="text-sm text-red-500 mt-2">{errors.secretKey.message}</p>
          )}
        </div>

        <div className="flex gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isConnecting}
            className="flex-1 h-12 text-base font-medium"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!isValid || isConnecting}
            className="flex-1 h-12 text-base font-semibold bg-primary hover:bg-primary/90"
          >
            {isConnecting ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              'Connect Exchange'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
