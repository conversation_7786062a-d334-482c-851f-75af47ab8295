import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ExchangeCredentials } from '@/types/exchange';
import {
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react';

const binanceCredentialsSchema = z.object({
  apiKey: z.string().min(1, 'API Key is required'),
  secretKey: z.string().min(1, 'Secret Key is required')
});

type BinanceCredentialsForm = z.infer<typeof binanceCredentialsSchema>;

interface BinanceConnectionFormModalProps {
  onSubmit: (credentials: ExchangeCredentials) => Promise<boolean>;
  onCancel: () => void;
  isConnecting: boolean;
  environment?: 'mainnet';
}

export const BinanceConnectionFormModal: React.FC<BinanceConnectionFormModalProps> = ({
  onSubmit,
  onCancel,
  isConnecting
}) => {
  const [showSecretKey, setShowSecretKey] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<BinanceCredentialsForm>({
    resolver: zodResolver(binanceCredentialsSchema),
    mode: 'onChange'
  });

  const handleFormSubmit = async (data: BinanceCredentialsForm) => {
    try {
      await onSubmit(data as ExchangeCredentials);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <div className="w-96 mx-auto p-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-3">Connect Binance</h2>
        <p className="text-muted-foreground text-sm">
          Enter your API credentials
        </p>
      </div>

      {/* IP Whitelist Info */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div className="text-sm">
            <p className="font-medium text-blue-900 mb-1">IP Whitelist Required</p>
            <p className="text-blue-700 mb-2">
              Add this IP address to your Binance API key restrictions:
            </p>
            <div className="bg-white border border-blue-300 rounded px-3 py-2 font-mono text-sm text-blue-900 select-all">
              *************
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="apiKey" className="text-sm font-medium text-foreground">
            API Key
          </Label>
          <Input
            id="apiKey"
            type="text"
            placeholder="Enter API key"
            {...register('apiKey')}
            className="h-11 text-sm"
          />
          {errors.apiKey && (
            <p className="text-xs text-red-500 mt-1">{errors.apiKey.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="secretKey" className="text-sm font-medium text-foreground">
            Secret Key
          </Label>
          <div className="relative">
            <Input
              id="secretKey"
              type={showSecretKey ? "text" : "password"}
              placeholder="Enter secret key"
              {...register('secretKey')}
              className="h-11 text-sm pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-11 w-10 p-0 hover:bg-transparent"
              onClick={() => setShowSecretKey(!showSecretKey)}
            >
              {showSecretKey ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.secretKey && (
            <p className="text-xs text-red-500 mt-1">{errors.secretKey.message}</p>
          )}
        </div>

        <div className="flex gap-3 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isConnecting}
            className="flex-1 h-11"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!isValid || isConnecting}
            className="flex-1 h-11 bg-primary hover:bg-primary/90"
          >
            {isConnecting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              'Connect'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
