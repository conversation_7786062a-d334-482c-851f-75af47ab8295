/**
 * Agent Management Hook
 * Provides state management and operations for trading agents
 */

import { useState, useEffect, useCallback } from 'react';
import { agentService, type Agent, type AgentStatus, type AgentConfiguration } from '@/services/agentService';
import { useToast } from '@/hooks/use-toast';

interface UseAgentsReturn {
  agents: Agent[];
  agentStatuses: Record<string, AgentStatus>;
  loading: boolean;
  creating: boolean;
  credentials: { hasCredentials: boolean; isMainnet: boolean };
  balance: { balance: number; available: number; success: boolean };
  
  // Actions
  loadAgents: () => Promise<void>;
  createFluxTraderAgent: (config?: Partial<AgentConfiguration>) => Promise<Agent | null>;
  startAgent: (agentId: string) => Promise<boolean>;
  stopAgent: (agentId: string) => Promise<boolean>;
  deleteAgent: (agentId: string) => Promise<boolean>;
  refreshAgentStatus: (agentId: string) => Promise<void>;
  refreshAllStatuses: () => Promise<void>;
  checkPrerequisites: () => Promise<void>;
}

export const useAgents = (): UseAgentsReturn => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentStatuses, setAgentStatuses] = useState<Record<string, AgentStatus>>({});
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [credentials, setCredentials] = useState({ hasCredentials: false, isMainnet: false });
  const [balance, setBalance] = useState({ balance: 0, available: 0, success: false });
  
  const { toast } = useToast();

  // Load agents and their statuses
  const loadAgents = useCallback(async () => {
    try {
      setLoading(true);
      const agentList = await agentService.getAgents();
      
      // Filter for FluxTrader agents
      const fluxTraderAgents = agentList.filter(agent => 
        agent.id.includes('fluxtrader') || 
        agent.strategy?.toLowerCase().includes('pump') ||
        agent.name?.toLowerCase().includes('flux')
      );
      
      setAgents(fluxTraderAgents);
      
      // Load statuses for each agent
      const statusPromises = fluxTraderAgents.map(async (agent) => {
        try {
          const status = await agentService.getAgentStatus(agent.id);
          return { agentId: agent.id, status };
        } catch (error) {
          console.error(`Failed to load status for agent ${agent.id}:`, error);
          return null;
        }
      });
      
      const statusResults = await Promise.all(statusPromises);
      const newStatuses: Record<string, AgentStatus> = {};
      
      statusResults.forEach(result => {
        if (result) {
          newStatuses[result.agentId] = result.status;
        }
      });
      
      setAgentStatuses(newStatuses);
    } catch (error) {
      console.error('Failed to load agents:', error);
      toast({
        title: "Error",
        description: "Failed to load trading agents",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Check prerequisites (credentials and balance)
  const checkPrerequisites = useCallback(async () => {
    try {
      const [credCheck, balanceCheck] = await Promise.all([
        agentService.checkCredentials(),
        agentService.checkBalance()
      ]);
      
      setCredentials(credCheck);
      setBalance(balanceCheck);
    } catch (error) {
      console.error('Failed to check prerequisites:', error);
    }
  }, []);

  // Create a new FluxTrader agent
  const createFluxTraderAgent = useCallback(async (config?: Partial<AgentConfiguration>): Promise<Agent | null> => {
    if (!credentials.hasCredentials) {
      toast({
        title: "Missing Credentials",
        description: "Please add your Binance API credentials first",
        variant: "destructive"
      });
      return null;
    }

    if (balance.available < 10) {
      toast({
        title: "Insufficient Balance",
        description: "Minimum $10 USDT balance recommended for trading",
        variant: "destructive"
      });
      return null;
    }

    try {
      setCreating(true);
      const newAgent = await agentService.createFluxTraderAgent(config);
      
      toast({
        title: "Agent Created",
        description: `FluxTrader agent created successfully`,
        variant: "default"
      });
      
      // Reload agents to include the new one
      await loadAgents();
      return newAgent;
    } catch (error) {
      console.error('Failed to create agent:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create FluxTrader agent",
        variant: "destructive"
      });
      return null;
    } finally {
      setCreating(false);
    }
  }, [credentials, balance, toast, loadAgents]);

  // Start an agent
  const startAgent = useCallback(async (agentId: string): Promise<boolean> => {
    try {
      const result = await agentService.startAgent(agentId);
      
      if (result.success) {
        toast({
          title: "Agent Started",
          description: "FluxTrader agent is now actively trading",
          variant: "default"
        });
        
        // Refresh the agent's status
        await refreshAgentStatus(agentId);
        return true;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Failed to start agent:', error);
      toast({
        title: "Start Failed",
        description: "Failed to start trading agent",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Stop an agent
  const stopAgent = useCallback(async (agentId: string): Promise<boolean> => {
    try {
      const result = await agentService.stopAgent(agentId);
      
      if (result.success) {
        toast({
          title: "Agent Stopped",
          description: "FluxTrader agent has been stopped",
          variant: "default"
        });
        
        // Refresh the agent's status
        await refreshAgentStatus(agentId);
        return true;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Failed to stop agent:', error);
      toast({
        title: "Stop Failed",
        description: "Failed to stop trading agent",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Delete an agent
  const deleteAgent = useCallback(async (agentId: string): Promise<boolean> => {
    try {
      const result = await agentService.deleteAgent(agentId);
      
      if (result.success) {
        toast({
          title: "Agent Deleted",
          description: "FluxTrader agent has been deleted",
          variant: "default"
        });
        
        // Remove from local state
        setAgents(prev => prev.filter(agent => agent.id !== agentId));
        setAgentStatuses(prev => {
          const newStatuses = { ...prev };
          delete newStatuses[agentId];
          return newStatuses;
        });
        
        return true;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Failed to delete agent:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete trading agent",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Refresh status for a specific agent
  const refreshAgentStatus = useCallback(async (agentId: string) => {
    try {
      const status = await agentService.getAgentStatus(agentId);
      setAgentStatuses(prev => ({ ...prev, [agentId]: status }));
    } catch (error) {
      console.error(`Failed to refresh status for agent ${agentId}:`, error);
    }
  }, []);

  // Refresh all agent statuses
  const refreshAllStatuses = useCallback(async () => {
    const statusPromises = agents.map(async (agent) => {
      try {
        const status = await agentService.getAgentStatus(agent.id);
        return { agentId: agent.id, status };
      } catch (error) {
        return null;
      }
    });
    
    const statusResults = await Promise.all(statusPromises);
    const newStatuses: Record<string, AgentStatus> = {};
    
    statusResults.forEach(result => {
      if (result) {
        newStatuses[result.agentId] = result.status;
      }
    });
    
    setAgentStatuses(newStatuses);
  }, [agents]);

  // Auto-refresh statuses every 10 seconds
  useEffect(() => {
    if (agents.length === 0) return;
    
    const interval = setInterval(() => {
      refreshAllStatuses();
    }, 10000);

    return () => clearInterval(interval);
  }, [agents, refreshAllStatuses]);

  // Load agents and check prerequisites on mount
  useEffect(() => {
    loadAgents();
    checkPrerequisites();
  }, [loadAgents, checkPrerequisites]);

  return {
    agents,
    agentStatuses,
    loading,
    creating,
    credentials,
    balance,
    
    // Actions
    loadAgents,
    createFluxTraderAgent,
    startAgent,
    stopAgent,
    deleteAgent,
    refreshAgentStatus,
    refreshAllStatuses,
    checkPrerequisites
  };
};
