import { useAuth } from '@/contexts/AuthContext';

export interface User {
  id: string;
  uuid?: string;
  name: string;
  full_name?: string;
  username?: string;
  email: string;
  role: string;
  avatar?: string;
  portfolioValue?: number;
  initials?: string;
  is_active?: boolean;
  is_verified?: boolean;
  is_superuser?: boolean;
  trading_experience?: string;
  risk_tolerance?: string;
  timezone?: string;
  created_at?: string;
  updated_at?: string;
  last_login?: string;
}

// Compatibility hook that wraps the auth context
export const useUser = () => {
  const { user, isLoading, isAuthenticated } = useAuth();

  const updateUser = (updates: Partial<User>) => {
    // In a real app, this would make an API call to update the user
    // For now, we'll just log the update
    console.log('User update requested:', updates);
    // You could implement this by adding an updateUser method to the auth context
  };

  return {
    user,
    loading: isLoading,
    updateUser,
    isAuthenticated
  };
};
