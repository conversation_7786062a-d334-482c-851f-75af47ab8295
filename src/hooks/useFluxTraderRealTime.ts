import { useState, useEffect, useCallback, useRef } from 'react';
import { websocketClient } from '@/services/websocketClient';

interface CycleAnalysisData {
  cycle: number;
  max_cycles: number;
  status: 'starting' | 'running' | 'completed' | 'error';
  pairs_analyzed: Array<{
    symbol: string;
    price: number;
    change_pct: number;
    volume: number;
    high_24h: number;
    low_24h: number;
  }>;
  signals_detected: Array<{
    symbol: string;
    signal_type: 'PUMP' | 'DUMP';
    confidence: number;
    momentum: number;
  }>;
  market_conditions: {
    total_pairs_analyzed: number;
    signals_found: number;
    cycle_duration: number;
    average_price_change: number;
    highest_volume_pair?: any;
    most_volatile_pair?: any;
    pairs_to_analyze?: string[];
    balance?: number;
    cycle_start_time?: string;
  };
  performance_metrics: {
    current_balance: number;
    signals_detection_rate: number;
    cycle_efficiency: number;
  };
  analysis_summary: string;
  next_cycle_eta?: string;
  timestamp: string;
}

interface TradeExecutionData {
  trade_id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  order_type: string;
  status: 'PENDING' | 'FILLED' | 'CANCELLED' | 'FAILED' | 'PARTIALLY_FILLED';
  filled_quantity?: number;
  average_price?: number;
  commission?: number;
  profit_loss?: number;
  order_id: string;
  execution_time?: string;
  error_message?: string;
  timestamp: string;
}

interface TradingEvent {
  id: string;
  type: 'trade' | 'order' | 'signal' | 'analysis' | 'error' | 'summary';
  timestamp: string;
  symbol?: string;
  action?: 'BUY' | 'SELL' | 'HOLD';
  amount?: number;
  price?: number;
  profit?: number;
  message: string;
  confidence?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'running' | 'detected';
  cycle?: number;
  max_cycles?: number;
  signals_found?: number;
  balance?: number;
  pairs_analyzed?: any[];
  signal_type?: 'PUMP' | 'DUMP';
  momentum?: number;
  change_24h?: number;
  signal_strength?: number;
}

interface UseFluxTraderRealTimeReturn {
  cycleAnalysis: CycleAnalysisData | null;
  tradeExecutions: TradeExecutionData[];
  tradingEvents: TradingEvent[];
  isConnected: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error';
  subscribeToAgent: (agentId: string) => Promise<void>;
  unsubscribeFromAgent: (agentId: string) => Promise<void>;
  clearData: () => void;
}

export function useFluxTraderRealTime(): UseFluxTraderRealTimeReturn {
  const [cycleAnalysis, setCycleAnalysis] = useState<CycleAnalysisData | null>(null);
  const [tradeExecutions, setTradeExecutions] = useState<TradeExecutionData[]>([]);
  const [tradingEvents, setTradingEvents] = useState<TradingEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting' | 'error'>('disconnected');
  
  const mountedRef = useRef(true);
  const subscribedAgentsRef = useRef<Set<string>>(new Set());

  // WebSocket event handlers
  const handleWebSocketConnected = useCallback(() => {
    if (!mountedRef.current) return;
    setIsConnected(true);
    setConnectionStatus('connected');
    console.log('🔌 [FluxTrader] WebSocket connected');

    // Immediately subscribe to the agent if we have an agentId
    const currentAgentId = window.location.pathname.split('/').pop();
    if (currentAgentId && currentAgentId !== 'agents') {
      console.log(`🔌 [FluxTrader] Auto-subscribing to agent ${currentAgentId} after connection`);
      setTimeout(() => {
        websocketClient.subscribeToAgent(currentAgentId).then(() => {
          console.log(`✅ [FluxTrader] Auto-subscription completed for ${currentAgentId}`);
        }).catch(error => {
          console.error(`❌ [FluxTrader] Auto-subscription failed for ${currentAgentId}:`, error);
        });
      }, 1000);
    }
  }, []);

  const handleWebSocketDisconnected = useCallback(() => {
    if (!mountedRef.current) return;
    setIsConnected(false);
    setConnectionStatus('disconnected');
    console.log('🔌 [FluxTrader] WebSocket disconnected');
  }, []);

  const handleWebSocketError = useCallback(() => {
    if (!mountedRef.current) return;
    setConnectionStatus('error');
    console.error('🔌 [FluxTrader] WebSocket error');
  }, []);

  const handleCycleAnalysis = useCallback((data: any) => {
    if (!mountedRef.current) return;
    
    console.log('📊 [FluxTrader] Received cycle analysis:', data);
    
    if (data.type === 'cycle_analysis' && data.data) {
      setCycleAnalysis({
        ...data.data,
        timestamp: data.timestamp || new Date().toISOString()
      });
    }
  }, []);

  const handleTradeExecution = useCallback((data: any) => {
    if (!mountedRef.current) return;
    
    console.log('💰 [FluxTrader] Received trade execution:', data);
    
    if (data.type === 'trade_execution' && data.data) {
      const tradeData = {
        ...data.data,
        timestamp: data.timestamp || new Date().toISOString()
      };
      
      setTradeExecutions(prev => {
        // Keep only the last 50 trades to prevent memory issues
        const updated = [tradeData, ...prev].slice(0, 50);
        return updated;
      });
    }
  }, []);

  const handleAgentUpdate = useCallback((data: any) => {
    if (!mountedRef.current) return;

    console.log('🤖 [FluxTrader] Received agent update:', data);

    if (data.type === 'agent_update' && data.event === 'trading_event' && data.data) {
      const eventData = data.data;
      const tradingEvent: TradingEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: eventData.type || 'signal',
        timestamp: eventData.timestamp || new Date().toISOString(),
        symbol: eventData.symbol,
        action: eventData.action,
        amount: eventData.amount,
        price: eventData.price,
        profit: eventData.profit,
        message: eventData.message || 'Trading event',
        confidence: eventData.confidence,
        status: eventData.status || 'completed',
        cycle: eventData.cycle,
        max_cycles: eventData.max_cycles,
        signals_found: eventData.signals_found,
        balance: eventData.balance,
        pairs_analyzed: eventData.pairs_analyzed,
        signal_type: eventData.signal_type,
        momentum: eventData.momentum,
        change_24h: eventData.change_24h,
        signal_strength: eventData.signal_strength
      };

      setTradingEvents(prev => {
        // Keep only the last 100 events to prevent memory issues
        const updated = [tradingEvent, ...prev].slice(0, 100);
        return updated;
      });
    }
  }, []);

  const handleSubscriptionConfirmed = useCallback((data: any) => {
    if (!mountedRef.current) return;

    console.log('✅ [FluxTrader] Subscription confirmed:', data);

    if (data.type === 'subscription_confirmed') {
      setConnectionStatus('connected');
      console.log(`✅ [FluxTrader] Successfully subscribed to agent ${data.agent_id}`);
    }
  }, []);

  // Subscribe to agent updates
  const subscribeToAgent = useCallback(async (agentId: string) => {
    try {
      if (subscribedAgentsRef.current.has(agentId)) {
        console.log(`🔌 [FluxTrader] Already subscribed to agent ${agentId}`);
        return;
      }

      setConnectionStatus('connecting');

      // Connect to WebSocket if not already connected
      if (!isConnected) {
        console.log(`🔌 [FluxTrader] Connecting to WebSocket first...`);
        await websocketClient.connect();
        // Wait a bit for connection to establish
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Subscribe to the specific agent
      console.log(`🔌 [FluxTrader] Subscribing to agent ${agentId}...`);
      await websocketClient.subscribeToAgent(agentId);
      subscribedAgentsRef.current.add(agentId);

      console.log(`🔌 [FluxTrader] Successfully subscribed to agent ${agentId}`);
      setConnectionStatus('connected');
    } catch (error) {
      console.error(`🔌 [FluxTrader] Failed to subscribe to agent ${agentId}:`, error);
      setConnectionStatus('error');
    }
  }, [isConnected]);

  // Unsubscribe from agent updates
  const unsubscribeFromAgent = useCallback(async (agentId: string) => {
    try {
      if (!subscribedAgentsRef.current.has(agentId)) {
        return;
      }

      await websocketClient.unsubscribeFromAgent(agentId);
      subscribedAgentsRef.current.delete(agentId);
      
      console.log(`🔌 [FluxTrader] Unsubscribed from agent ${agentId}`);
    } catch (error) {
      console.error(`🔌 [FluxTrader] Failed to unsubscribe from agent ${agentId}:`, error);
    }
  }, []);

  // Clear all data
  const clearData = useCallback(() => {
    setCycleAnalysis(null);
    setTradeExecutions([]);
    setTradingEvents([]);
  }, []);

  // Setup WebSocket event listeners
  useEffect(() => {
    if (!mountedRef.current) return;

    // Setup event listeners
    websocketClient.on('connected', handleWebSocketConnected);
    websocketClient.on('disconnected', handleWebSocketDisconnected);
    websocketClient.on('error', handleWebSocketError);
    websocketClient.on('cycle_analysis', handleCycleAnalysis);
    websocketClient.on('trade_execution', handleTradeExecution);
    websocketClient.on('agent_update', handleAgentUpdate);
    websocketClient.on('subscription_confirmed', handleSubscriptionConfirmed);

    return () => {
      websocketClient.off('connected', handleWebSocketConnected);
      websocketClient.off('disconnected', handleWebSocketDisconnected);
      websocketClient.off('error', handleWebSocketError);
      websocketClient.off('cycle_analysis', handleCycleAnalysis);
      websocketClient.off('trade_execution', handleTradeExecution);
      websocketClient.off('agent_update', handleAgentUpdate);
      websocketClient.off('subscription_confirmed', handleSubscriptionConfirmed);
    };
  }, [
    handleWebSocketConnected,
    handleWebSocketDisconnected,
    handleWebSocketError,
    handleCycleAnalysis,
    handleTradeExecution,
    handleAgentUpdate,
    handleSubscriptionConfirmed
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // Unsubscribe from all agents
      subscribedAgentsRef.current.forEach(agentId => {
        websocketClient.unsubscribeFromAgent(agentId).catch(console.error);
      });
      subscribedAgentsRef.current.clear();
    };
  }, []);

  return {
    cycleAnalysis,
    tradeExecutions,
    tradingEvents,
    isConnected,
    connectionStatus,
    subscribeToAgent,
    unsubscribeFromAgent,
    clearData
  };
}
