import { describe, it, expect } from 'vitest'
import React from 'react'

describe('App', () => {
  it('should pass basic test', () => {
    expect(1 + 1).toBe(2)
  })

  it('should validate environment', () => {
    expect(typeof window).toBe('object')
    expect(typeof document).toBe('object')
  })

  it('should have React available', () => {
    expect(React).toBeDefined()
    expect(typeof React.createElement).toBe('function')
  })
})
