import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  apiClient, 
  ExchangeCredentials, 
  CreateExchangeCredentials, 
  ConnectionTestRequest,
  ConnectionTestResult,
  SupportedExchange 
} from '@/services/api';

interface ExchangeContextType {
  // Exchange credentials
  exchanges: ExchangeCredentials[];
  supportedExchanges: SupportedExchange[];
  selectedExchange: ExchangeCredentials | null;
  
  // Loading states
  isLoading: boolean;
  isConnecting: boolean;
  isTesting: boolean;
  
  // Actions
  loadExchanges: () => Promise<void>;
  loadSupportedExchanges: () => Promise<void>;
  createExchange: (credentials: CreateExchangeCredentials) => Promise<{ success: boolean; error?: string }>;
  updateExchange: (id: number, updates: Partial<CreateExchangeCredentials>) => Promise<{ success: boolean; error?: string }>;
  deleteExchange: (id: number) => Promise<{ success: boolean; error?: string }>;
  testConnection: (connectionData: ConnectionTestRequest) => Promise<ConnectionTestResult>;
  validateCredentials: (id: number) => Promise<ConnectionTestResult>;
  selectExchange: (exchange: ExchangeCredentials | null) => void;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

const EnhancedExchangeContext = createContext<ExchangeContextType | undefined>(undefined);

export const useEnhancedExchange = () => {
  const context = useContext(EnhancedExchangeContext);
  if (context === undefined) {
    throw new Error('useEnhancedExchange must be used within an EnhancedExchangeProvider');
  }
  return context;
};

interface ExchangeProviderProps {
  children: ReactNode;
}

export const EnhancedExchangeProvider: React.FC<ExchangeProviderProps> = ({ children }) => {
  const [exchanges, setExchanges] = useState<ExchangeCredentials[]>([]);
  const [supportedExchanges, setSupportedExchanges] = useState<SupportedExchange[]>([]);
  const [selectedExchange, setSelectedExchange] = useState<ExchangeCredentials | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user's exchanges on mount
  useEffect(() => {
    loadExchanges();
    loadSupportedExchanges();
  }, []);

  const loadExchanges = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const userExchanges = await apiClient.getMyExchanges();
      setExchanges(userExchanges);
      
      // If no exchange is selected and we have exchanges, select the first active one
      if (!selectedExchange && userExchanges.length > 0) {
        const activeExchange = userExchanges.find(ex => ex.status === 'active') || userExchanges[0];
        setSelectedExchange(activeExchange);
      }
    } catch (error: any) {
      console.error('Error loading exchanges:', error);
      setError(error.response?.data?.detail || 'Failed to load exchanges');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSupportedExchanges = async (): Promise<void> => {
    try {
      const supported = await apiClient.getSupportedExchanges();
      setSupportedExchanges(supported);
    } catch (error: any) {
      console.error('Error loading supported exchanges:', error);
      // Don't set error for this as it's not critical
    }
  };

  const createExchange = async (credentials: CreateExchangeCredentials): Promise<{ success: boolean; error?: string }> => {
    setIsConnecting(true);
    setError(null);
    
    try {
      const newExchange = await apiClient.createExchangeCredentials(credentials);
      setExchanges(prev => [...prev, newExchange]);
      
      // Select the new exchange if it's the first one
      if (exchanges.length === 0) {
        setSelectedExchange(newExchange);
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Error creating exchange:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to create exchange credentials';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsConnecting(false);
    }
  };

  const updateExchange = async (id: number, updates: Partial<CreateExchangeCredentials>): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedExchange = await apiClient.updateExchangeCredentials(id, updates);
      setExchanges(prev => prev.map(ex => ex.id === id ? updatedExchange : ex));
      
      // Update selected exchange if it's the one being updated
      if (selectedExchange?.id === id) {
        setSelectedExchange(updatedExchange);
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Error updating exchange:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to update exchange credentials';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const deleteExchange = async (id: number): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await apiClient.deleteExchangeCredentials(id);
      setExchanges(prev => prev.filter(ex => ex.id !== id));
      
      // Clear selected exchange if it's the one being deleted
      if (selectedExchange?.id === id) {
        const remainingExchanges = exchanges.filter(ex => ex.id !== id);
        setSelectedExchange(remainingExchanges.length > 0 ? remainingExchanges[0] : null);
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Error deleting exchange:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to delete exchange credentials';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async (connectionData: ConnectionTestRequest): Promise<ConnectionTestResult> => {
    setIsTesting(true);
    setError(null);
    
    try {
      const result = await apiClient.testConnection(connectionData);
      return result;
    } catch (error: any) {
      console.error('Error testing connection:', error);
      const errorMessage = error.response?.data?.detail || 'Connection test failed';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage,
        error_code: 'CONNECTION_ERROR'
      };
    } finally {
      setIsTesting(false);
    }
  };

  const validateCredentials = async (id: number): Promise<ConnectionTestResult> => {
    setIsTesting(true);
    setError(null);
    
    try {
      const result = await apiClient.validateCredentials(id);
      
      // Update the exchange status based on validation result
      if (result.success) {
        setExchanges(prev => prev.map(ex => 
          ex.id === id ? { ...ex, status: 'active' as const } : ex
        ));
      } else {
        setExchanges(prev => prev.map(ex => 
          ex.id === id ? { ...ex, status: 'error' as const } : ex
        ));
      }
      
      return result;
    } catch (error: any) {
      console.error('Error validating credentials:', error);
      const errorMessage = error.response?.data?.detail || 'Credential validation failed';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage,
        error_code: 'VALIDATION_ERROR'
      };
    } finally {
      setIsTesting(false);
    }
  };

  const selectExchange = (exchange: ExchangeCredentials | null): void => {
    setSelectedExchange(exchange);
    // Optionally store selection in localStorage
    if (exchange) {
      localStorage.setItem('kamikaze_selected_exchange', exchange.id.toString());
    } else {
      localStorage.removeItem('kamikaze_selected_exchange');
    }
  };

  const clearError = (): void => {
    setError(null);
  };

  const value: ExchangeContextType = {
    exchanges,
    supportedExchanges,
    selectedExchange,
    isLoading,
    isConnecting,
    isTesting,
    loadExchanges,
    loadSupportedExchanges,
    createExchange,
    updateExchange,
    deleteExchange,
    testConnection,
    validateCredentials,
    selectExchange,
    error,
    clearError,
  };

  return <EnhancedExchangeContext.Provider value={value}>{children}</EnhancedExchangeContext.Provider>;
};
