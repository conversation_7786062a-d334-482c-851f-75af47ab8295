/**
 * Exchange Service for handling Binance connections and credential management
 */

import { apiClient } from './api';

export interface BinanceCredentials {
  apiKey: string;
  secretKey: string;
  testnet: boolean;
}

export interface ConnectionResult {
  success: boolean;
  message?: string;
  error?: string;
  account_info?: any;
  environment?: string;
}

export class ExchangeService {
  /**
   * Test Binance connection and save credentials if successful
   */
  async testBinanceConnection(credentials: BinanceCredentials): Promise<ConnectionResult> {
    console.log('🔧 [ExchangeService] Testing Binance connection...', {
      apiKey: credentials.apiKey?.substring(0, 8) + '...',
      testnet: credentials.testnet,
      timestamp: new Date().toISOString()
    });

    try {
      // Step 1: Test the connection first
      console.log('📡 [ExchangeService] Step 1: Testing connection...');
      const testResult = await apiClient.testConnection({
        api_key: credentials.apiKey,
        secret_key: credentials.secretKey,
        is_testnet: credentials.testnet
      });

      console.log('📋 [ExchangeService] Connection test result:', {
        success: testResult.success,
        message: testResult.message,
        environment: testResult.environment
      });

      if (!testResult.success) {
        return {
          success: false,
          error: testResult.message || 'Connection test failed'
        };
      }

      // Step 2: Save credentials if connection test passed
      console.log('💾 [ExchangeService] Step 2: Saving credentials...');
      let saveResult;
      
      if (credentials.testnet) {
        // Save to testnet_credentials table
        saveResult = await apiClient.saveTestnetCredentials({
          exchange: 'binance',
          api_key: credentials.apiKey,
          secret_key: credentials.secretKey
        });
      } else {
        // Save to binance_credentials table
        saveResult = await apiClient.saveBinanceCredentials({
          api_key: credentials.apiKey,
          secret_key: credentials.secretKey,
          is_mainnet: true
        });
      }

      console.log('📋 [ExchangeService] Save credentials result:', {
        success: saveResult.success,
        message: saveResult.message
      });

      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.message || 'Failed to save credentials'
        };
      }

      // Step 3: Return success with account info
      console.log('✅ [ExchangeService] Connection and save successful!');
      return {
        success: true,
        message: testResult.message,
        account_info: testResult.account_info,
        environment: testResult.environment
      };

    } catch (error: any) {
      console.error('💥 [ExchangeService] Error in testBinanceConnection:', error);
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Unknown error occurred'
      };
    }
  }

  /**
   * Test connection only (without saving credentials)
   */
  async testConnectionOnly(credentials: BinanceCredentials): Promise<ConnectionResult> {
    console.log('🧪 [ExchangeService] Testing connection only...', {
      apiKey: credentials.apiKey?.substring(0, 8) + '...',
      testnet: credentials.testnet
    });

    try {
      const testResult = await apiClient.testConnection({
        api_key: credentials.apiKey,
        secret_key: credentials.secretKey,
        is_testnet: credentials.testnet
      });

      return {
        success: testResult.success,
        message: testResult.message,
        error: testResult.success ? undefined : testResult.message,
        account_info: testResult.account_info,
        environment: testResult.environment
      };

    } catch (error: any) {
      console.error('💥 [ExchangeService] Error in testConnectionOnly:', error);
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Connection test failed'
      };
    }
  }

  /**
   * Save credentials without testing (use with caution)
   */
  async saveCredentials(credentials: BinanceCredentials): Promise<ConnectionResult> {
    console.log('💾 [ExchangeService] Saving credentials...', {
      apiKey: credentials.apiKey?.substring(0, 8) + '...',
      testnet: credentials.testnet
    });

    try {
      let saveResult;
      
      if (credentials.testnet) {
        saveResult = await apiClient.saveTestnetCredentials({
          exchange: 'binance',
          api_key: credentials.apiKey,
          secret_key: credentials.secretKey
        });
      } else {
        saveResult = await apiClient.saveBinanceCredentials({
          api_key: credentials.apiKey,
          secret_key: credentials.secretKey,
          is_mainnet: true
        });
      }

      return {
        success: saveResult.success,
        message: saveResult.message,
        error: saveResult.success ? undefined : saveResult.message
      };

    } catch (error: any) {
      console.error('💥 [ExchangeService] Error in saveCredentials:', error);
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Failed to save credentials'
      };
    }
  }
}

// Export singleton instance
export const exchangeService = new ExchangeService();
