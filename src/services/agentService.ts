/**
 * Agent Service
 * Handles all agent management operations with automatic authentication
 */

import { apiClient } from './api';

export interface AgentConfiguration {
  trading_pairs: string[];
  risk_level: 'low' | 'medium' | 'high';
  leverage: number;
  trade_amount_usdt: number;
  pump_threshold: number;
  dump_threshold: number;
  min_confidence: number;
  signal_strength_threshold: number;
  min_24h_change: number;
  max_cycles: number;
  enable_real_trades: boolean;
  stop_loss: number;
  take_profit: number;
}

export interface AgentCreateRequest {
  agent_type: 'pump_dump' | 'arbitrage' | 'dca' | 'grid' | 'market_making';
  agent_id?: string;
  configuration?: Partial<AgentConfiguration>;
}

export interface AgentStatus {
  agent_id: string;
  status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error';
  is_running: boolean;
  uptime_seconds: number;
  current_cycle: number;
  max_cycles: number;
  last_activity?: string;
  mcp_connected: boolean;
  binance_connected: boolean;
  groq_connected: boolean;
  error_message?: string;
}

export interface Agent {
  id: string;
  name: string;
  status: string;
  strategy: string;
  profit: number;
  profitPercentage: number;
  trades: number;
  winRate: number;
  lastTrade?: string;
  riskLevel: 'low' | 'medium' | 'high';
  configuration: AgentConfiguration;
  created_at: string;
  updated_at: string;
}

class AgentService {
  private baseUrl = '/api/v1/agents';

  /**
   * Get all agents for the current user
   */
  async getAgents(): Promise<Agent[]> {
    try {
      const response = await apiClient.get(this.baseUrl);
      return response.data || [];
    } catch (error) {
      console.error('Failed to get agents:', error);
      throw error;
    }
  }

  /**
   * Get agent status
   */
  async getAgentStatus(agentId: string): Promise<AgentStatus> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${agentId}/status`);
      // Backend returns { status: "success", agent_id: "...", data: {...} }
      // We need to return the data object which contains the actual agent status
      if (response.data?.status === 'success' && response.data?.data) {
        return response.data.data;
      }
      throw new Error('Invalid response format from agent status API');
    } catch (error) {
      console.error(`Failed to get agent status for ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new FluxTrader agent with default configuration
   */
  async createFluxTraderAgent(customConfig?: Partial<AgentConfiguration>): Promise<Agent> {
    const defaultConfig: AgentConfiguration = {
      trading_pairs: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
      risk_level: 'medium',
      leverage: 20,
      trade_amount_usdt: 4.0,
      pump_threshold: 0.03,
      dump_threshold: -0.03,
      min_confidence: 35,
      signal_strength_threshold: 0.4,
      min_24h_change: 0.01,
      max_cycles: 100,
      enable_real_trades: true,
      stop_loss: 1.5,
      take_profit: 2.0,
      ...customConfig
    };

    const agentRequest: AgentCreateRequest = {
      agent_type: 'pump_dump',
      agent_id: `fluxtrader_${Date.now()}`,
      configuration: defaultConfig
    };

    try {
      const response = await apiClient.post(`${this.baseUrl}/create`, agentRequest);
      if (response.data?.status === 'success') {
        return response.data.agent;
      }
      throw new Error(response.data?.message || 'Failed to create agent');
    } catch (error) {
      console.error('Failed to create FluxTrader agent:', error);
      throw error;
    }
  }

  /**
   * Start an agent
   */
  async startAgent(agentId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${agentId}/start`);
      return {
        success: response.data?.status === 'success',
        message: response.data?.message || 'Agent started successfully'
      };
    } catch (error) {
      console.error(`Failed to start agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Stop an agent
   */
  async stopAgent(agentId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${agentId}/stop`);
      return {
        success: response.data?.status === 'success',
        message: response.data?.message || 'Agent stopped successfully'
      };
    } catch (error) {
      console.error(`Failed to stop agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an agent
   */
  async deleteAgent(agentId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/${agentId}`);
      return {
        success: response.data?.status === 'success',
        message: response.data?.message || 'Agent deleted successfully'
      };
    } catch (error) {
      console.error(`Failed to delete agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Check if user has valid Binance credentials - prioritize mainnet for real trading
   */
  async checkCredentials(): Promise<{ hasCredentials: boolean; isMainnet: boolean }> {
    try {
      const response = await apiClient.get('/api/v1/credentials/binance');
      const data = response.data;

      if (data.success && data.data) {
        // Prioritize mainnet credentials for real trading
        const hasMainnet = data.data.mainnet || false;
        const hasTestnet = data.data.testnet || false;
        const hasAnyCredentials = data.data.has_credentials || hasMainnet || hasTestnet;

        return {
          hasCredentials: hasAnyCredentials,
          isMainnet: hasMainnet // Use mainnet if available, otherwise false
        };
      }

      return { hasCredentials: false, isMainnet: false };
    } catch (error) {
      console.error('Failed to check credentials:', error);
      return { hasCredentials: false, isMainnet: false };
    }
  }

  /**
   * Check account balance
   */
  async checkBalance(): Promise<{ balance: number; available: number; success: boolean }> {
    try {
      const response = await apiClient.get('/api/v1/trading/account/balance');
      const data = response.data;
      
      if (data.success && data.data) {
        return {
          balance: data.data.total_balance || 0,
          available: data.data.available_balance || 0,
          success: true
        };
      }
      
      return { balance: 0, available: 0, success: false };
    } catch (error) {
      console.error('Failed to check balance:', error);
      return { balance: 0, available: 0, success: false };
    }
  }
}

export const agentService = new AgentService();
