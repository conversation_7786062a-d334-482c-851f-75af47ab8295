import { appConfig } from '../config/appConfig';

const API_BASE_URL = `${appConfig.API_BASE_URL}/api/v1/auth`;

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: any;
  access_token?: string;
  refresh_token?: string;
  token_type?: string;
}

export interface SignUpRequest {
  name: string;
  email: string;
  password: string;
}

export interface SignInRequest {
  email: string;
  password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface UserSession {
  session_id: string;
  ip_address?: string;
  user_agent?: string;
  device_info?: string;
  location?: string;
  is_active: boolean;
  created_at: string;
  last_activity: string;
  expires_at: string;
}

class AuthApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('kamikaze_access_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  async signUp(userData: SignUpRequest): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();
      
      if (data.success && data.access_token) {
        // Store tokens
        localStorage.setItem('kamikaze_access_token', data.access_token);
        localStorage.setItem('kamikaze_refresh_token', data.refresh_token);
        localStorage.setItem('kamikaze_user', JSON.stringify(data.user));
      }

      return data;
    } catch (error) {
      console.error('Sign up error:', error);
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
      };
    }
  }

  async signIn(credentials: SignInRequest): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();
      
      if (data.success && data.access_token) {
        // Store tokens
        localStorage.setItem('kamikaze_access_token', data.access_token);
        localStorage.setItem('kamikaze_refresh_token', data.refresh_token);
        localStorage.setItem('kamikaze_user', JSON.stringify(data.user));
      }

      return data;
    } catch (error) {
      console.error('Sign in error:', error);
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
      };
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    try {
      const refreshToken = localStorage.getItem('kamikaze_refresh_token');
      
      if (!refreshToken) {
        return {
          success: false,
          message: 'No refresh token available',
        };
      }

      const response = await fetch(`${API_BASE_URL}/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      const data = await response.json();
      
      if (data.success && data.access_token) {
        // Update access token
        localStorage.setItem('kamikaze_access_token', data.access_token);
        localStorage.setItem('kamikaze_user', JSON.stringify(data.user));
      }

      return data;
    } catch (error) {
      console.error('Token refresh error:', error);
      return {
        success: false,
        message: 'Failed to refresh authentication token',
      };
    }
  }

  async logout(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      const data = await response.json();
      
      // Clear local storage regardless of API response
      this.clearAuthData();
      
      return data;
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local storage even if API call fails
      this.clearAuthData();
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  async getCurrentUser(): Promise<{ success: boolean; user?: any; message?: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/me`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      const data = await response.json();
      
      if (data.success && data.user) {
        localStorage.setItem('kamikaze_user', JSON.stringify(data.user));
      }

      return data;
    } catch (error) {
      console.error('Get current user error:', error);
      return {
        success: false,
        message: 'Failed to get user information',
      };
    }
  }

  async getUserSessions(): Promise<{ success: boolean; sessions?: UserSession[]; message?: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Get user sessions error:', error);
      return {
        success: false,
        message: 'Failed to get user sessions',
      };
    }
  }

  async revokeSession(sessionId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Revoke session error:', error);
      return {
        success: false,
        message: 'Failed to revoke session',
      };
    }
  }

  clearAuthData(): void {
    localStorage.removeItem('kamikaze_access_token');
    localStorage.removeItem('kamikaze_refresh_token');
    localStorage.removeItem('kamikaze_user');
  }

  getStoredUser(): any | null {
    try {
      const userStr = localStorage.getItem('kamikaze_user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      return null;
    }
  }

  getStoredToken(): string | null {
    return localStorage.getItem('kamikaze_access_token');
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  async validateAndRefreshToken(): Promise<boolean> {
    const token = this.getStoredToken();
    
    if (!token) {
      return false;
    }

    if (this.isTokenExpired(token)) {
      const refreshResult = await this.refreshToken();
      return refreshResult.success;
    }

    return true;
  }
}

export const authApi = new AuthApiService();