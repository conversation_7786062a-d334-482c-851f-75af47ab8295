import { appConfig } from '../config/appConfig';
import { authApi } from './authApi';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = appConfig.API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = authApi.getStoredToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();
      
      if (response.status === 401) {
        // Token might be expired, try to refresh
        const refreshResult = await authApi.refreshToken();
        if (!refreshResult.success) {
          // Refresh failed, redirect to login
          authApi.clearAuthData();
          window.location.href = '/';
          throw new Error('Authentication failed');
        }
        // Token refreshed, but don't retry the original request automatically
        // Let the calling code handle the retry
      }

      return {
        success: response.ok,
        data: data,
        message: data.message,
        error: data.error
      };
    } catch (error) {
      console.error('API response error:', error);
      return {
        success: false,
        error: 'Failed to parse response'
      };
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('GET request error:', error);
      return {
        success: false,
        error: 'Network error'
      };
    }
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('POST request error:', error);
      return {
        success: false,
        error: 'Network error'
      };
    }
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('PUT request error:', error);
      return {
        success: false,
        error: 'Network error'
      };
    }
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers,
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('DELETE request error:', error);
      return {
        success: false,
        error: 'Network error'
      };
    }
  }

  // Market data endpoints
  async getMarketData(symbols: string[]): Promise<ApiResponse> {
    return this.get(`/api/v1/market/data?symbols=${symbols.join(',')}`);
  }

  async getTicker(symbol: string): Promise<ApiResponse> {
    return this.get(`/api/v1/market/ticker/${symbol}`);
  }

  async getKlines(symbol: string, interval: string = '1h', limit: number = 100): Promise<ApiResponse> {
    return this.get(`/api/v1/market/klines/${symbol}?interval=${interval}&limit=${limit}`);
  }

  // Trading endpoints
  async getAccountBalance(): Promise<ApiResponse> {
    return this.get('/api/v1/trading/account/balance');
  }

  async placeOrder(orderData: any): Promise<ApiResponse> {
    return this.post('/api/v1/trading/orders/place', orderData);
  }

  async getPositions(): Promise<ApiResponse> {
    return this.get('/api/v1/trading/positions');
  }

  // Agent endpoints
  async getAgents(): Promise<ApiResponse> {
    return this.get('/api/v1/agents/');
  }

  async getAgentStatus(agentId: string): Promise<ApiResponse> {
    return this.get(`/api/v1/agents/${agentId}/status`);
  }

  async startAgent(agentId: string): Promise<ApiResponse> {
    return this.post(`/api/v1/agents/${agentId}/start`);
  }

  async stopAgent(agentId: string): Promise<ApiResponse> {
    return this.post(`/api/v1/agents/${agentId}/stop`);
  }

  // Database endpoints
  async getDatabaseHealth(): Promise<ApiResponse> {
    return this.get('/api/database/health');
  }

  async executeQuery(query: string, params?: any[]): Promise<ApiResponse> {
    return this.post('/api/database/query', { query, params });
  }
}

export const apiClient = new ApiClient();