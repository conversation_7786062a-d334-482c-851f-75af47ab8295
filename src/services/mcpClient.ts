import { ExchangeCredentials, MCPResponse, BinanceAccountInfo } from '@/types/exchange';

interface MCPRequest {
  jsonrpc: string;
  id: number;
  method: string;
  params: {
    name: string;
    arguments: Record<string, unknown>;
  };
}

interface MCPToolResponse {
  jsonrpc: string;
  id: number;
  result?: {
    content: Array<{
      type: string;
      text: string;
    }>;
  };
  error?: {
    code: number;
    message: string;
  };
}

class MCPClient {
  private websocket: WebSocket | null = null;
  private requestId: number = 1;
  private pendingRequests: Map<number, {
    resolve: (value: unknown) => void;
    reject: (reason?: unknown) => void;
  }> = new Map();
  private connected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;
  private wsUrl: string;

  constructor(wsUrl: string = 'ws://127.0.0.1:8000/ws') {
    this.wsUrl = wsUrl;
    console.log('🔧 [MCP] Initialized MCP Client for unified backend on port 8000');
  }

  async connect(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        console.log(`🔗 Attempting to connect to MCP server at ${this.wsUrl}`);
        this.websocket = new WebSocket(this.wsUrl);

        this.websocket.onopen = () => {
          console.log('✅ [MCP] Connected to MCP server at', this.wsUrl);
          this.connected = true;
          this.reconnectAttempts = 0;
          resolve(true);
        };

        this.websocket.onmessage = (event) => {
          try {
            const response: MCPToolResponse = JSON.parse(event.data);
            this.handleResponse(response);
          } catch (error) {
            console.error('Failed to parse MCP response:', error);
          }
        };

        this.websocket.onclose = () => {
          console.log('🔌 Disconnected from MCP server');
          this.connected = false;
          this.handleReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error('❌ MCP WebSocket error:', error);
          this.connected = false;
          // Don't resolve false immediately, let timeout handle it
        };

        // Timeout for connection
        setTimeout(() => {
          if (!this.connected) {
            console.log('⏰ MCP connection timeout - proceeding with direct API testing');
            resolve(true); // Resolve true to allow direct API testing
          }
        }, 3000); // Reduced timeout

      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        resolve(true); // Still resolve true to allow direct API testing
      }
    });
  }

  private handleResponse(response: MCPToolResponse): void {
    const { id, result, error } = response;
    const pendingRequest = this.pendingRequests.get(id);

    if (pendingRequest) {
      this.pendingRequests.delete(id);

      if (error) {
        pendingRequest.reject(new Error(error.message));
      } else if (result?.content?.[0]?.text) {
        try {
          const data = JSON.parse(result.content[0].text);
          pendingRequest.resolve(data);
        } catch {
          pendingRequest.resolve(result.content[0].text);
        }
      } else {
        pendingRequest.resolve(result);
      }
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  private async callTool(toolName: string, args: Record<string, unknown> = {}): Promise<unknown> {
    if (!this.connected || !this.websocket) {
      throw new Error('Not connected to MCP server');
    }

    const requestId = this.requestId++;
    const request: MCPRequest = {
      jsonrpc: "2.0",
      id: requestId,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(requestId, { resolve, reject });
      
      this.websocket!.send(JSON.stringify(request));

      // Timeout for request
      setTimeout(() => {
        if (this.pendingRequests.has(requestId)) {
          this.pendingRequests.delete(requestId);
          reject(new Error('Request timeout'));
        }
      }, 10000);
    });
  }

  async testBinanceConnection(credentials: ExchangeCredentials): Promise<MCPResponse> {
    console.log('🔗 [MCP] testBinanceConnection called with:', {
      apiKey: credentials.apiKey?.substring(0, 8) + '...',
      secretKey: credentials.secretKey?.substring(0, 8) + '...',
      testnet: credentials.testnet,
      connected: this.connected,
      hasWebSocket: !!this.websocket,
      websocketReadyState: this.websocket?.readyState
    });

    // Enhanced validation
    if (!credentials.apiKey || !credentials.secretKey) {
      console.error('❌ [MCP] Missing credentials');
      return {
        success: false,
        error: 'API Key and Secret Key are required'
      };
    }

    try {
      // Try MCP bridge first (unified backend on port 8000)
      console.log('🌉 [MCP] Attempting connection to unified backend...');

      // Ensure we're connected to the unified backend
      if (!this.connected || !this.websocket) {
        console.log('🔗 [MCP] Not connected, attempting to connect...');
        const connected = await this.connect();
        if (!connected) {
          console.log('❌ [MCP] Failed to connect to unified backend');
          // Fallback to direct API testing
          return await this.testBinanceDirectly(credentials);
        }
      }

      try {
        console.log('📡 [MCP] Calling test_binance_connection tool via unified backend...');
        const result = await this.callTool('test_binance_connection', {
          api_key: credentials.apiKey,
          secret_key: credentials.secretKey,
          testnet: credentials.testnet || false
        });
        console.log('📨 [MCP] Unified backend result:', result);

        const response = {
          success: result.success || false,
          data: result.data,
          error: result.error
        };
        console.log('📤 [MCP] Returning response:', response);
        return response;
      } catch (mcpError) {
        console.log('❌ [MCP] Unified backend failed, falling back to direct API:', mcpError);
        // Fallback to direct API testing
        const fallbackResult = await this.testBinanceDirectly(credentials);
        console.log('📤 [MCP] Fallback result:', fallbackResult);
        return fallbackResult;
      }
    } catch (error) {
      console.error('💥 [MCP] Error in testBinanceConnection:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async testBinanceDirectly(credentials: ExchangeCredentials): Promise<MCPResponse> {
    console.log('🔗 [DIRECT] Starting direct Binance API test...');
    console.log('🔍 [DIRECT] Credentials validation:', {
      hasApiKey: !!credentials.apiKey,
      hasSecretKey: !!credentials.secretKey,
      apiKeyLength: credentials.apiKey?.length || 0,
      secretKeyLength: credentials.secretKey?.length || 0,
      testnet: credentials.testnet
    });

    try {
      // Create a test request to Binance Futures API
      const baseUrl = credentials.testnet
        ? 'https://testnet.binancefuture.com'
        : 'https://fapi.binance.com';

      const endpoint = '/fapi/v2/account';

      console.log(`🌐 [DIRECT] Using ${credentials.testnet ? 'Futures testnet' : 'Futures mainnet'} endpoint: ${baseUrl}`);

      const timestamp = Date.now();
      const queryString = `timestamp=${timestamp}`;

      console.log('🔐 [DIRECT] Creating signature...');
      // Create signature
      const signature = await this.createSignature(queryString, credentials.secretKey);
      const signedQuery = `${queryString}&signature=${signature}`;

      const url = `${baseUrl}${endpoint}?${signedQuery}`;
      console.log('📡 [DIRECT] Making request to:', url.replace(/signature=[^&]+/, 'signature=***'));

      // Test account endpoint
      console.log('🚀 [DIRECT] Making fetch request...');
      console.log('⚠️ [DIRECT] Note: Direct API calls may fail due to CORS restrictions');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-MBX-APIKEY': credentials.apiKey,
          'Content-Type': 'application/json'
        },
        mode: 'cors'
      });

      console.log('📨 [DIRECT] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          data: {
            accountType: "FUTURES",
            totalWalletBalance: data.totalWalletBalance,
            totalUnrealizedProfit: data.totalUnrealizedProfit,
            totalMarginBalance: data.totalMarginBalance,
            canTrade: data.canTrade,
            canWithdraw: data.canWithdraw,
            canDeposit: data.canDeposit,
            assetsCount: data.assets?.length || 0,
            positionsCount: data.positions?.length || 0
          }
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.msg || `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      console.error('💥 [DIRECT] Error in direct API test:', error);
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      console.error('🔍 [DIRECT] Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  private async createSignature(queryString: string, secretKey: string): Promise<string> {
    console.log('🔐 [SIGNATURE] Creating signature for query:', queryString.substring(0, 50) + '...');
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secretKey),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(queryString));
    const hexSignature = Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    console.log('🔐 [SIGNATURE] Generated signature:', hexSignature.substring(0, 16) + '...');
    return hexSignature;
  }

  // Quick test function for the provided credentials
  async testProvidedCredentials(): Promise<MCPResponse> {
    const testCredentials: ExchangeCredentials = {
      apiKey: '25798b2faa58e081b8030086324b4c8dfab485409497e27532f3a2e96169b8b6',
      secretKey: '0c15c03c3f0f3265d27e856b3464dbb549234421f00cdea70087f09477f1fea8',
      testnet: true
    };

    console.log('🧪 [TEST] Testing provided testnet credentials...');
    return this.testBinanceConnection(testCredentials);
  }

  // Simple standalone test that bypasses all MCP infrastructure
  async simpleCredentialTest(credentials: ExchangeCredentials): Promise<MCPResponse> {
    console.log('🔍 [SIMPLE] Starting simple credential test...');
    console.log('🔍 [SIMPLE] Credentials:', {
      hasApiKey: !!credentials.apiKey,
      hasSecretKey: !!credentials.secretKey,
      apiKeyLength: credentials.apiKey?.length,
      secretKeyLength: credentials.secretKey?.length,
      testnet: credentials.testnet
    });

    try {
      const baseUrl = credentials.testnet
        ? 'https://testnet.binancefuture.com'
        : 'https://fapi.binance.com';

      // Simple ping test first
      console.log('🏓 [SIMPLE] Testing basic Futures API connectivity...');
      const pingResponse = await fetch(`${baseUrl}/fapi/v1/ping`);
      console.log('🏓 [SIMPLE] Futures ping response:', pingResponse.status);

      if (!pingResponse.ok) {
        return {
          success: false,
          error: `Binance Futures API not accessible: ${pingResponse.status}`
        };
      }

      // Test server time
      console.log('⏰ [SIMPLE] Testing Futures server time...');
      const timeResponse = await fetch(`${baseUrl}/fapi/v1/time`);
      const timeData = await timeResponse.json();
      console.log('⏰ [SIMPLE] Futures server time:', timeData);

      // Test account endpoint with credentials
      console.log('🔐 [SIMPLE] Testing Futures account access...');
      const timestamp = Date.now();
      const queryString = `timestamp=${timestamp}`;
      const signature = await this.createSignature(queryString, credentials.secretKey);
      const signedQuery = `${queryString}&signature=${signature}`;

      const accountResponse = await fetch(`${baseUrl}/fapi/v2/account?${signedQuery}`, {
        headers: {
          'X-MBX-APIKEY': credentials.apiKey
        }
      });

      console.log('🔐 [SIMPLE] Futures account response:', accountResponse.status);

      if (accountResponse.ok) {
        const accountData = await accountResponse.json();
        return {
          success: true,
          data: {
            accountType: "FUTURES",
            canTrade: accountData.canTrade,
            totalWalletBalance: accountData.totalWalletBalance,
            totalUnrealizedProfit: accountData.totalUnrealizedProfit,
            assetsCount: accountData.assets?.length || 0,
            positionsCount: accountData.positions?.length || 0
          }
        };
      } else {
        const errorData = await accountResponse.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.msg || `HTTP ${accountResponse.status}`
        };
      }

    } catch (error) {
      console.error('💥 [SIMPLE] Simple test error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async getBinanceAccountInfo(credentials: ExchangeCredentials): Promise<BinanceAccountInfo | null> {
    try {
      const result = await this.callTool('get_account_balance', {
        api_key: credentials.apiKey,
        secret_key: credentials.secretKey,
        testnet: credentials.testnet || false
      });

      if (result.success && result.data) {
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('Failed to get Binance account info:', error);
      return null;
    }
  }

  async getServerStatus(): Promise<MCPResponse> {
    try {
      const result = await this.callTool('get_server_status');
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  disconnect(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.connected = false;
    this.pendingRequests.clear();
  }

  isConnected(): boolean {
    return this.connected;
  }
}

// Singleton instance
export const mcpClient = new MCPClient();
