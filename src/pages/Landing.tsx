import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { SignInModal } from '@/components/auth/SignInModal';
import { RegisterModal } from '@/components/auth/RegisterModal';
import './Landing.css';

const Landing = () => {
  const navigate = useNavigate();
  const portfolioChartRef = useRef<HTMLCanvasElement>(null);
  const { isAuthenticated } = useAuth();

  // Modal states
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);

  useEffect(() => {
    // Redirect to dashboard if already authenticated
    if (isAuthenticated) {
      navigate('/dashboard');
      return;
    }

    // Initialize animations and interactions
    initializeAnimations();
    initializeCharts();
    initializeScrollEffects();

    // Add window resize handler for responsive chart
    const handleResize = () => {
      if (portfolioChartRef.current) {
        setTimeout(() => {
          drawPortfolioChart(portfolioChartRef.current!);
        }, 100);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      // Clean up any event listeners or intervals if needed
    };
  }, [isAuthenticated, navigate]);

  const openApp = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      setIsRegisterModalOpen(true);
    }
  };

  const handleSignIn = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      setIsSignInModalOpen(true);
    }
  };

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      setIsRegisterModalOpen(true);
    }
  };

  const initializeAnimations = () => {
    const observerOptions = {
      threshold: 0.15,
      rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add('animate-in');
          }, index * 150);
        }
      });
    }, observerOptions);
    
    const animateElements = document.querySelectorAll('.feature-card, .analytics-card, .pricing-card, .stat-item');
    animateElements.forEach(element => {
      observer.observe(element);
    });
  };

  const initializeCharts = () => {
    if (portfolioChartRef.current) {
      // Add a small delay for smooth initialization
      setTimeout(() => {
        drawPortfolioChart(portfolioChartRef.current!);
      }, 100);
    }
    animateTradingChart();
  };

  const drawPortfolioChart = (canvas: HTMLCanvasElement) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set up high-DPI canvas
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);

    const width = rect.width;
    const height = rect.height;
    const padding = { top: 20, right: 20, bottom: 30, left: 20 };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // Enhanced realistic portfolio data with timestamps
    const portfolioData = [
      { time: '00:00', value: 100000, change: 0 },
      { time: '04:00', value: 102500, change: 2.5 },
      { time: '08:00', value: 108200, change: 8.2 },
      { time: '12:00', value: 115600, change: 15.6 },
      { time: '16:00', value: 119800, change: 19.8 },
      { time: '20:00', value: 125430, change: 25.43 },
      { time: '24:00', value: 125430, change: 25.43 }
    ];

    // Clear canvas with subtle background
    ctx.clearRect(0, 0, width, height);

    // Draw background grid
    drawGrid(ctx, padding, chartWidth, chartHeight);

    // Draw area gradient
    drawAreaGradient(ctx, portfolioData, padding, chartWidth, chartHeight);

    // Draw main line with glow effect
    drawMainLine(ctx, portfolioData, padding, chartWidth, chartHeight);

    // Draw data points with hover effects
    drawDataPoints(ctx, portfolioData, padding, chartWidth, chartHeight);

    // Draw trend indicators
    drawTrendIndicators(ctx, portfolioData, padding, chartWidth, chartHeight);

    // Add interactive hover functionality
    addChartInteractivity(canvas, portfolioData, padding, chartWidth, chartHeight);
  };

  // Helper function to draw background grid
  const drawGrid = (ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number) => {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
    ctx.lineWidth = 1;

    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = padding.top + (chartHeight / 4) * i;
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(padding.left + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 6; i++) {
      const x = padding.left + (chartWidth / 6) * i;
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, padding.top + chartHeight);
      ctx.stroke();
    }
  };

  // Helper function to draw area gradient
  const drawAreaGradient = (ctx: CanvasRenderingContext2D, data: any[], padding: any, chartWidth: number, chartHeight: number) => {
    const minValue = Math.min(...data.map(d => d.value));
    const maxValue = Math.max(...data.map(d => d.value));
    const valueRange = maxValue - minValue;

    // Create sophisticated gradient
    const gradient = ctx.createLinearGradient(0, padding.top, 0, padding.top + chartHeight);
    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.4)');
    gradient.addColorStop(0.3, 'rgba(20, 184, 166, 0.3)');
    gradient.addColorStop(0.7, 'rgba(139, 92, 246, 0.2)');
    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.05)');

    ctx.beginPath();
    data.forEach((point, index) => {
      const x = padding.left + (index / (data.length - 1)) * chartWidth;
      const y = padding.top + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        // Use quadratic curves for smooth lines
        const prevPoint = data[index - 1];
        const prevX = padding.left + ((index - 1) / (data.length - 1)) * chartWidth;
        const prevY = padding.top + chartHeight - ((prevPoint.value - minValue) / valueRange) * chartHeight;
        const cpX = (prevX + x) / 2;
        ctx.quadraticCurveTo(cpX, prevY, x, y);
      }
    });

    // Close the path for area fill
    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
    ctx.lineTo(padding.left, padding.top + chartHeight);
    ctx.closePath();
    ctx.fillStyle = gradient;
    ctx.fill();
  };

  // Helper function to draw main line with glow effect
  const drawMainLine = (ctx: CanvasRenderingContext2D, data: any[], padding: any, chartWidth: number, chartHeight: number) => {
    const minValue = Math.min(...data.map(d => d.value));
    const maxValue = Math.max(...data.map(d => d.value));
    const valueRange = maxValue - minValue;

    // Draw glow effect
    ctx.shadowColor = '#3b82f6';
    ctx.shadowBlur = 15;
    ctx.lineWidth = 4;
    ctx.strokeStyle = 'rgba(59, 130, 246, 0.8)';

    ctx.beginPath();
    data.forEach((point, index) => {
      const x = padding.left + (index / (data.length - 1)) * chartWidth;
      const y = padding.top + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        const prevPoint = data[index - 1];
        const prevX = padding.left + ((index - 1) / (data.length - 1)) * chartWidth;
        const prevY = padding.top + chartHeight - ((prevPoint.value - minValue) / valueRange) * chartHeight;
        const cpX = (prevX + x) / 2;
        ctx.quadraticCurveTo(cpX, prevY, x, y);
      }
    });
    ctx.stroke();

    // Reset shadow for main line
    ctx.shadowBlur = 0;
    ctx.lineWidth = 3;

    // Create line gradient
    const lineGradient = ctx.createLinearGradient(padding.left, 0, padding.left + chartWidth, 0);
    lineGradient.addColorStop(0, '#3b82f6');
    lineGradient.addColorStop(0.5, '#14b8a6');
    lineGradient.addColorStop(1, '#8b5cf6');
    ctx.strokeStyle = lineGradient;

    ctx.beginPath();
    data.forEach((point, index) => {
      const x = padding.left + (index / (data.length - 1)) * chartWidth;
      const y = padding.top + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        const prevPoint = data[index - 1];
        const prevX = padding.left + ((index - 1) / (data.length - 1)) * chartWidth;
        const prevY = padding.top + chartHeight - ((prevPoint.value - minValue) / valueRange) * chartHeight;
        const cpX = (prevX + x) / 2;
        ctx.quadraticCurveTo(cpX, prevY, x, y);
      }
    });
    ctx.stroke();
  };

  // Helper function to draw data points
  const drawDataPoints = (ctx: CanvasRenderingContext2D, data: any[], padding: any, chartWidth: number, chartHeight: number) => {
    const minValue = Math.min(...data.map(d => d.value));
    const maxValue = Math.max(...data.map(d => d.value));
    const valueRange = maxValue - minValue;

    data.forEach((point, index) => {
      const x = padding.left + (index / (data.length - 1)) * chartWidth;
      const y = padding.top + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;

      // Outer glow
      ctx.beginPath();
      ctx.arc(x, y, 8, 0, Math.PI * 2);
      ctx.fillStyle = 'rgba(59, 130, 246, 0.3)';
      ctx.fill();

      // Main point
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, Math.PI * 2);
      const pointGradient = ctx.createRadialGradient(x, y, 0, x, y, 5);
      pointGradient.addColorStop(0, '#ffffff');
      pointGradient.addColorStop(0.7, '#3b82f6');
      pointGradient.addColorStop(1, '#1e40af');
      ctx.fillStyle = pointGradient;
      ctx.fill();

      // Border
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    });
  };

  // Helper function to draw trend indicators
  const drawTrendIndicators = (ctx: CanvasRenderingContext2D, data: any[], padding: any, chartWidth: number, chartHeight: number) => {
    const minValue = Math.min(...data.map(d => d.value));
    const maxValue = Math.max(...data.map(d => d.value));
    const valueRange = maxValue - minValue;

    // Draw trend arrow at the end
    const lastPoint = data[data.length - 1];
    const x = padding.left + chartWidth - 30;
    const y = padding.top + chartHeight - ((lastPoint.value - minValue) / valueRange) * chartHeight;

    // Trend arrow (upward)
    ctx.fillStyle = '#10b981';
    ctx.beginPath();
    ctx.moveTo(x, y - 8);
    ctx.lineTo(x + 6, y + 2);
    ctx.lineTo(x - 6, y + 2);
    ctx.closePath();
    ctx.fill();

    // Performance indicator text
    ctx.fillStyle = '#10b981';
    ctx.font = 'bold 12px Inter, sans-serif';
    ctx.textAlign = 'right';
    ctx.fillText('+24.5%', x - 10, y - 12);
  };

  // Helper function to add chart interactivity
  const addChartInteractivity = (canvas: HTMLCanvasElement, data: any[], padding: any, chartWidth: number, chartHeight: number) => {
    const tooltip = document.createElement('div');
    tooltip.className = 'chart-tooltip';
    tooltip.style.cssText = `
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s ease;
      z-index: 1000;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(59, 130, 246, 0.3);
    `;
    document.body.appendChild(tooltip);

    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Check if mouse is over a data point
      const minValue = Math.min(...data.map(d => d.value));
      const maxValue = Math.max(...data.map(d => d.value));
      const valueRange = maxValue - minValue;

      let hoveredPoint = null;
      data.forEach((point, index) => {
        const pointX = padding.left + (index / (data.length - 1)) * chartWidth;
        const pointY = padding.top + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;

        const distance = Math.sqrt((x - pointX) ** 2 + (y - pointY) ** 2);
        if (distance < 15) {
          hoveredPoint = { ...point, index };
        }
      });

      if (hoveredPoint) {
        tooltip.innerHTML = `
          <div style="font-weight: bold; margin-bottom: 4px;">${hoveredPoint.time}</div>
          <div>Value: $${hoveredPoint.value.toLocaleString()}</div>
          <div style="color: #10b981;">Change: +${hoveredPoint.change.toFixed(2)}%</div>
        `;
        tooltip.style.opacity = '1';
        tooltip.style.left = `${e.clientX + 10}px`;
        tooltip.style.top = `${e.clientY - 10}px`;
        canvas.style.cursor = 'pointer';
      } else {
        tooltip.style.opacity = '0';
        canvas.style.cursor = 'default';
      }
    };

    const handleMouseLeave = () => {
      tooltip.style.opacity = '0';
      canvas.style.cursor = 'default';
    };

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseleave', handleMouseLeave);

    // Cleanup function
    return () => {
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    };
  };

  const animateTradingChart = () => {
    const chartLine = document.querySelector('.chart-line') as SVGPathElement;
    if (chartLine) {
      const pathLength = chartLine.getTotalLength();
      chartLine.style.strokeDasharray = pathLength.toString();
      chartLine.style.strokeDashoffset = pathLength.toString();
      
      setTimeout(() => {
        chartLine.style.transition = 'stroke-dashoffset 2s ease-in-out';
        chartLine.style.strokeDashoffset = '0';
      }, 500);
    }
  };

  const initializeScrollEffects = () => {
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const heroVisual = document.querySelector('.hero-visual') as HTMLElement;
      
      if (heroVisual) {
        const rate = scrolled * -0.5;
        heroVisual.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  };

  return (
    <div className="landing-page">
      {/* Navigation Header */}
      <header className="header-nav">
        <nav className="nav-container">
          <div className="nav-brand">
            <div className="brand-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="url(#brandGradient)" stroke="currentColor" strokeWidth="1"/>
                <defs>
                  <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style={{stopColor:'#3b82f6'}}/>
                    <stop offset="100%" style={{stopColor:'#14b8a6'}}/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <span className="brand-text">Kamikaze</span>
            <span className="brand-badge">PRO</span>
          </div>
          
          <div className="nav-menu" id="navMenu">
            <a href="#features" className="nav-link">Features</a>
            <a href="#analytics" className="nav-link">Analytics</a>
            <a href="#pricing" className="nav-link">Pricing</a>
            <a href="#about" className="nav-link">About</a>
            <a href="#contact" className="nav-link">Contact</a>
          </div>
          
          <div className="nav-actions">
            <button className="btn-secondary" onClick={handleSignIn}>Sign In</button>
            <button className="btn-primary" onClick={handleGetStarted}>Get Started</button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-background">
          <div className="hero-gradient-1"></div>
          <div className="hero-gradient-2"></div>
          <div className="hero-gradient-3"></div>
        </div>
        
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              <span className="title-line-1">The Future of</span>
              <span className="title-line-2">
                <span className="gradient-text-enhanced">AI Trading</span>
              </span>
              <span className="title-line-3">Starts Here</span>
            </h1>
            
            <p className="hero-description">
              Experience the next generation of cryptocurrency trading with our advanced AI-powered platform. 
              Automated strategies, real-time analytics, and intelligent risk management - all in one place.
            </p>

            <div className="hero-actions">
              <button className="btn-hero-primary" onClick={openApp}>
                <div className="btn-content">
                  <span>Start Trading Now</span>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="btn-glow"></div>
              </button>
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="visual-background">
              <div className="floating-element element-1"></div>
              <div className="floating-element element-2"></div>
              <div className="floating-element element-3"></div>
            </div>
            
            <div className="premium-dashboard-mockup">
              <div className="mockup-glow-premium"></div>

              {/* Modern Browser Header */}
              <div className="mockup-browser-header">
                <div className="browser-controls">
                  <div className="control-dot red"></div>
                  <div className="control-dot yellow"></div>
                  <div className="control-dot green"></div>
                </div>
                <div className="browser-url">
                  <div className="url-bar">
                    <span className="url-protocol">https://</span>
                    <span className="url-domain">app.kamikaze.trading</span>
                    <div className="ssl-indicator">🔒</div>
                  </div>
                </div>
                <div className="browser-actions">
                  <div className="live-status">
                    <div className="live-pulse"></div>
                    <span>LIVE</span>
                  </div>
                </div>
              </div>

              {/* Dashboard Content */}
              <div className="premium-dashboard-content">
                {/* Top Navigation Bar */}
                <div className="dashboard-topbar">
                  <div className="topbar-left">
                    <div className="logo-section">
                      <div className="logo-icon">⚡</div>
                      <span className="logo-text">Kamikaze</span>
                    </div>
                    <div className="nav-tabs">
                      <div className="nav-tab active">Dashboard</div>
                      <div className="nav-tab">Trading</div>
                      <div className="nav-tab">Portfolio</div>
                    </div>
                  </div>
                  <div className="topbar-right">
                    <div className="portfolio-summary">
                      <span className="portfolio-value">$127,450.32</span>
                      <span className="portfolio-change positive">+$3,240 (2.61%)</span>
                    </div>
                    <div className="user-avatar">AT</div>
                  </div>
                </div>

                {/* Main Dashboard Grid */}
                <div className="dashboard-grid">
                  {/* Market Overview Cards */}
                  <div className="market-cards">
                    <div className="market-card btc">
                      <div className="card-header">
                        <div className="crypto-info">
                          <div className="crypto-icon">₿</div>
                          <div className="crypto-details">
                            <span className="crypto-symbol">BTC/USDT</span>
                            <span className="crypto-name">Bitcoin</span>
                          </div>
                        </div>
                        <div className="price-info">
                          <span className="current-price">$67,234.50</span>
                          <span className="price-change positive">+4.23%</span>
                        </div>
                      </div>
                      <div className="mini-chart">
                        <svg viewBox="0 0 120 40" className="price-chart">
                          <defs>
                            <linearGradient id="btcGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="#f7931a" stopOpacity="0.3"/>
                              <stop offset="100%" stopColor="#f7931a" stopOpacity="0"/>
                            </linearGradient>
                          </defs>
                          <path d="M0,30 Q30,25 60,20 T120,15" stroke="#f7931a" strokeWidth="2" fill="none"/>
                          <path d="M0,30 Q30,25 60,20 T120,15 L120,40 L0,40 Z" fill="url(#btcGradient)"/>
                        </svg>
                      </div>
                    </div>

                    <div className="market-card eth">
                      <div className="card-header">
                        <div className="crypto-info">
                          <div className="crypto-icon">Ξ</div>
                          <div className="crypto-details">
                            <span className="crypto-symbol">ETH/USDT</span>
                            <span className="crypto-name">Ethereum</span>
                          </div>
                        </div>
                        <div className="price-info">
                          <span className="current-price">$3,456.78</span>
                          <span className="price-change positive">+2.87%</span>
                        </div>
                      </div>
                      <div className="mini-chart">
                        <svg viewBox="0 0 120 40" className="price-chart">
                          <defs>
                            <linearGradient id="ethGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="#627eea" stopOpacity="0.3"/>
                              <stop offset="100%" stopColor="#627eea" stopOpacity="0"/>
                            </linearGradient>
                          </defs>
                          <path d="M0,25 Q30,28 60,22 T120,18" stroke="#627eea" strokeWidth="2" fill="none"/>
                          <path d="M0,25 Q30,28 60,22 T120,18 L120,40 L0,40 Z" fill="url(#ethGradient)"/>
                        </svg>
                      </div>
                    </div>

                    <div className="market-card sol">
                      <div className="card-header">
                        <div className="crypto-info">
                          <div className="crypto-icon">◎</div>
                          <div className="crypto-details">
                            <span className="crypto-symbol">SOL/USDT</span>
                            <span className="crypto-name">Solana</span>
                          </div>
                        </div>
                        <div className="price-info">
                          <span className="current-price">$198.45</span>
                          <span className="price-change negative">-1.23%</span>
                        </div>
                      </div>
                      <div className="mini-chart">
                        <svg viewBox="0 0 120 40" className="price-chart">
                          <defs>
                            <linearGradient id="solGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="#9945ff" stopOpacity="0.3"/>
                              <stop offset="100%" stopColor="#9945ff" stopOpacity="0"/>
                            </linearGradient>
                          </defs>
                          <path d="M0,20 Q30,18 60,25 T120,28" stroke="#9945ff" strokeWidth="2" fill="none"/>
                          <path d="M0,20 Q30,18 60,25 T120,28 L120,40 L0,40 Z" fill="url(#solGradient)"/>
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Trading Bots Section */}
                  <div className="trading-bots-panel">
                    <div className="panel-header">
                      <h3 className="panel-title">Active Trading Bots</h3>
                      <div className="bots-count">3 Running</div>
                    </div>
                    <div className="bots-list">
                      <div className="bot-item">
                        <div className="bot-status running"></div>
                        <div className="bot-info">
                          <span className="bot-name">DCA Master</span>
                          <span className="bot-pair">BTC/USDT</span>
                        </div>
                        <div className="bot-performance">
                          <span className="bot-profit positive">+12.4%</span>
                          <span className="bot-trades">47 trades</span>
                        </div>
                      </div>
                      <div className="bot-item">
                        <div className="bot-status running"></div>
                        <div className="bot-info">
                          <span className="bot-name">Grid Trader</span>
                          <span className="bot-pair">ETH/USDT</span>
                        </div>
                        <div className="bot-performance">
                          <span className="bot-profit positive">+8.7%</span>
                          <span className="bot-trades">23 trades</span>
                        </div>
                      </div>
                      <div className="bot-item">
                        <div className="bot-status paused"></div>
                        <div className="bot-info">
                          <span className="bot-name">Momentum</span>
                          <span className="bot-pair">SOL/USDT</span>
                        </div>
                        <div className="bot-performance">
                          <span className="bot-profit negative">-2.1%</span>
                          <span className="bot-trades">12 trades</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Main Chart Area */}
                  <div className="main-chart-container">
                    <div className="chart-header">
                      <div className="chart-title-section">
                        <h3 className="chart-title">BTC/USDT</h3>
                        <div className="chart-price-info">
                          <span className="chart-price">$67,234.50</span>
                          <span className="chart-change positive">+$2,847.23 (+4.23%)</span>
                        </div>
                      </div>
                      <div className="chart-controls">
                        <div className="timeframe-buttons">
                          <button className="timeframe-btn">1m</button>
                          <button className="timeframe-btn">5m</button>
                          <button className="timeframe-btn active">1h</button>
                          <button className="timeframe-btn">4h</button>
                          <button className="timeframe-btn">1d</button>
                        </div>
                        <div className="chart-tools">
                          <button className="tool-btn">📊</button>
                          <button className="tool-btn">📈</button>
                          <button className="tool-btn">⚙️</button>
                        </div>
                      </div>
                    </div>
                    <div className="advanced-chart">
                      <svg viewBox="0 0 500 200" className="trading-chart-advanced">
                        <defs>
                          <linearGradient id="advancedGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.4"/>
                            <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.2"/>
                            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.1"/>
                          </linearGradient>
                          <filter id="chartGlow">
                            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                            <feMerge>
                              <feMergeNode in="coloredBlur"/>
                              <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                          </filter>
                        </defs>
                        {/* Grid lines */}
                        <g className="chart-grid" opacity="0.1">
                          <line x1="0" y1="40" x2="500" y2="40" stroke="#ffffff"/>
                          <line x1="0" y1="80" x2="500" y2="80" stroke="#ffffff"/>
                          <line x1="0" y1="120" x2="500" y2="120" stroke="#ffffff"/>
                          <line x1="0" y1="160" x2="500" y2="160" stroke="#ffffff"/>
                          <line x1="100" y1="0" x2="100" y2="200" stroke="#ffffff"/>
                          <line x1="200" y1="0" x2="200" y2="200" stroke="#ffffff"/>
                          <line x1="300" y1="0" x2="300" y2="200" stroke="#ffffff"/>
                          <line x1="400" y1="0" x2="400" y2="200" stroke="#ffffff"/>
                        </g>
                        {/* Main price line */}
                        <path d="M0,160 Q50,140 100,120 T200,100 Q250,90 300,85 T400,75 Q450,70 500,65"
                              stroke="#3b82f6" strokeWidth="3" fill="none" filter="url(#chartGlow)"/>
                        {/* Fill area */}
                        <path d="M0,160 Q50,140 100,120 T200,100 Q250,90 300,85 T400,75 Q450,70 500,65 L500,200 L0,200 Z"
                              fill="url(#advancedGradient)"/>
                        {/* Volume bars */}
                        <g className="volume-bars" opacity="0.6">
                          <rect x="0" y="180" width="8" height="20" fill="#06b6d4"/>
                          <rect x="20" y="175" width="8" height="25" fill="#06b6d4"/>
                          <rect x="40" y="170" width="8" height="30" fill="#06b6d4"/>
                          <rect x="60" y="185" width="8" height="15" fill="#06b6d4"/>
                          <rect x="80" y="165" width="8" height="35" fill="#06b6d4"/>
                        </g>
                        {/* Current price indicator */}
                        <circle cx="500" cy="65" r="5" fill="#3b82f6" className="price-indicator">
                          <animate attributeName="r" values="5;7;5" dur="2s" repeatCount="indefinite"/>
                        </circle>
                      </svg>
                    </div>
                    <div className="chart-metrics-advanced">
                      <div className="metric-group">
                        <div className="metric-item">
                          <span className="metric-label">24h Volume</span>
                          <span className="metric-value">$2.4B</span>
                        </div>
                        <div className="metric-item">
                          <span className="metric-label">Market Cap</span>
                          <span className="metric-value">$1.32T</span>
                        </div>
                      </div>
                      <div className="metric-group">
                        <div className="metric-item">
                          <span className="metric-label">24h High</span>
                          <span className="metric-value">$68,450</span>
                        </div>
                        <div className="metric-item">
                          <span className="metric-label">24h Low</span>
                          <span className="metric-value">$64,120</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Portfolio Summary */}
                  <div className="portfolio-summary-panel">
                    <div className="panel-header">
                      <h3 className="panel-title">Portfolio Overview</h3>
                      <div className="portfolio-total">$127,450.32</div>
                    </div>
                    <div className="portfolio-breakdown">
                      <div className="asset-item">
                        <div className="asset-info">
                          <span className="asset-symbol">BTC</span>
                          <span className="asset-amount">1.89 BTC</span>
                        </div>
                        <div className="asset-value">
                          <span className="asset-usd">$127,073.51</span>
                          <span className="asset-percentage">89.7%</span>
                        </div>
                      </div>
                      <div className="asset-item">
                        <div className="asset-info">
                          <span className="asset-symbol">ETH</span>
                          <span className="asset-amount">2.45 ETH</span>
                        </div>
                        <div className="asset-value">
                          <span className="asset-usd">$8,469.11</span>
                          <span className="asset-percentage">6.6%</span>
                        </div>
                      </div>
                      <div className="asset-item">
                        <div className="asset-info">
                          <span className="asset-symbol">SOL</span>
                          <span className="asset-amount">24.7 SOL</span>
                        </div>
                        <div className="asset-value">
                          <span className="asset-usd">$4,901.72</span>
                          <span className="asset-percentage">3.8%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section" id="features">
        <div className="section-container">
          <div className="section-header">
            <div className="section-badge">
              <span>Core Features</span>
            </div>
            <h2 className="section-title">
              <span className="title-main">Powerful Features</span>
              <span className="title-sub">Built for Success</span>
            </h2>
            <p className="section-description">
              Everything you need to dominate the crypto markets with confidence and precision.
              Our advanced platform combines cutting-edge technology with intuitive design.
            </p>
          </div>

          <div className="features-grid">
            <div className="feature-card primary">
              <div className="feature-header">
                <div className="feature-icon">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="currentColor" opacity="0.2"/>
                    <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" stroke="currentColor" strokeWidth="2"/>
                    <circle cx="16" cy="16" r="4" fill="currentColor"/>
                  </svg>
                </div>
                <div className="feature-badge">AI Powered</div>
              </div>
              <div className="feature-content">
                <h3 className="feature-title">AI Trading Bots</h3>
                <p className="feature-description">
                  Advanced machine learning algorithms that adapt to market conditions and execute trades 24/7.
                  Our bots learn from market patterns and optimize strategies in real-time.
                </p>
                <div className="feature-stats">
                  <div className="stat">
                    <span className="stat-number">95%</span>
                    <span className="stat-label">Success Rate</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">24/7</span>
                    <span className="stat-label">Active Trading</span>
                  </div>
                </div>
                <div className="feature-highlights">
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Machine Learning Optimization</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Multi-Exchange Support</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Risk-Adjusted Strategies</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="feature-card secondary">
              <div className="feature-header">
                <div className="feature-icon">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <rect x="4" y="4" width="24" height="24" rx="4" fill="currentColor" opacity="0.2"/>
                    <rect x="4" y="4" width="24" height="24" rx="4" stroke="currentColor" strokeWidth="2"/>
                    <path d="M8 12h16M8 16h16M8 20h12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                    <circle cx="24" cy="8" r="2" fill="currentColor"/>
                  </svg>
                </div>
                <div className="feature-badge">Real-time</div>
              </div>
              <div className="feature-content">
                <h3 className="feature-title">Advanced Analytics</h3>
                <p className="feature-description">
                  Comprehensive market analysis with live charts, technical indicators, and performance metrics.
                  Make informed decisions with our professional-grade analytics suite.
                </p>
                <div className="feature-stats">
                  <div className="stat">
                    <span className="stat-number">50+</span>
                    <span className="stat-label">Indicators</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">1ms</span>
                    <span className="stat-label">Data Latency</span>
                  </div>
                </div>
                <div className="feature-highlights">
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Professional Charts</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Custom Indicators</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Market Sentiment Analysis</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="feature-card tertiary">
              <div className="feature-header">
                <div className="feature-icon">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <circle cx="16" cy="16" r="12" fill="currentColor" opacity="0.2"/>
                    <circle cx="16" cy="16" r="12" stroke="currentColor" strokeWidth="2"/>
                    <path d="M12 16l4 4 8-8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="16" cy="16" r="6" stroke="currentColor" strokeWidth="1" opacity="0.5"/>
                  </svg>
                </div>
                <div className="feature-badge">Protected</div>
              </div>
              <div className="feature-content">
                <h3 className="feature-title">Risk Management</h3>
                <p className="feature-description">
                  Intelligent stop-loss, take-profit, and portfolio diversification tools to protect your capital.
                  Advanced risk controls ensure your investments are always protected.
                </p>
                <div className="feature-stats">
                  <div className="stat">
                    <span className="stat-number">99.8%</span>
                    <span className="stat-label">Capital Protection</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">5+</span>
                    <span className="stat-label">Risk Models</span>
                  </div>
                </div>
                <div className="feature-highlights">
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Smart Stop-Loss</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Portfolio Diversification</span>
                  </div>
                  <div className="highlight-item">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>Real-time Monitoring</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="features-cta">
            <div className="cta-content">
              <h3 className="cta-title">Ready to Experience the Future?</h3>
              <p className="cta-description">Join thousands of successful traders already using our platform</p>
            </div>
            <button className="cta-button" onClick={openApp}>
              <span>Start Your Journey</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* Analytics Section */}
      <section className="analytics-section" id="analytics">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">
              <span className="title-main">Real-time Insights</span>
              <span className="title-sub"></span>
            </h2>
            <p className="section-description">
              Advanced market insights and performance tracking to keep you ahead of the curve.
            </p>
          </div>

          <div className="analytics-grid">
            {/* Active Bots Card - Now Large (spans 2 rows) */}
            <div className="analytics-card large">
              <div className="card-header">
                <h3 className="card-title">Active Bots</h3>
                <div className="status-indicator active">12 Running</div>
              </div>
              <div className="card-content">
                <div className="bot-list">
                  <div className="bot-item">
                    <span className="bot-name">BTC Scalper Pro</span>
                    <span className="bot-profit positive">+12.3%</span>
                  </div>
                  <div className="bot-item">
                    <span className="bot-name">ETH DCA Strategy</span>
                    <span className="bot-profit positive">+8.7%</span>
                  </div>
                  <div className="bot-item">
                    <span className="bot-name">Altcoin Hunter</span>
                    <span className="bot-profit positive">+15.2%</span>
                  </div>
                  <div className="bot-item">
                    <span className="bot-name">SOL Grid Trader</span>
                    <span className="bot-profit positive">+9.4%</span>
                  </div>
                  <div className="bot-item">
                    <span className="bot-name">MATIC Momentum</span>
                    <span className="bot-profit positive">+6.8%</span>
                  </div>
                </div>
                <div className="bot-stats">
                  <div className="stat-item">
                    <span className="stat-label">Total Trades Today</span>
                    <span className="stat-value">247</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Success Rate</span>
                    <span className="stat-value">78.5%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Portfolio Performance Card - Now smaller (top-right position) */}
            <div className="analytics-card">
              <div className="card-header">
                <h3 className="card-title">Portfolio Performance</h3>
                <div className="performance-badge positive">+24.5%</div>
              </div>
              <div className="chart-container">
                <canvas ref={portfolioChartRef} className="portfolio-chart" width="400" height="200"></canvas>
              </div>
              <div className="metrics-row">
                <div className="metric-item">
                  <span className="metric-label">Total Value</span>
                  <span className="metric-value">$125,430</span>
                </div>
                <div className="metric-item">
                  <span className="metric-label">24h Change</span>
                  <span className="metric-value positive">+$3,240</span>
                </div>
              </div>
            </div>

            {/* Market Signals Card - Bottom Full Width */}
            <div className="analytics-card">
              <div className="card-header">
                <h3 className="card-title">Market Signals</h3>
                <div className="signal-strength strong">Strong</div>
              </div>
              <div className="card-content">
                <div className="signals-grid">
                  <div className="signal-item">
                    <div className="signal-left">
                      <div className="signal-icon buy">↗</div>
                      <div className="signal-info">
                        <span className="signal-pair">BTC/USDT</span>
                        <span className="signal-type">Buy Signal</span>
                      </div>
                    </div>
                    <span className="signal-confidence">95%</span>
                  </div>
                  <div className="signal-item">
                    <div className="signal-left">
                      <div className="signal-icon sell">↘</div>
                      <div className="signal-info">
                        <span className="signal-pair">ETH/USDT</span>
                        <span className="signal-type">Sell Signal</span>
                      </div>
                    </div>
                    <span className="signal-confidence">87%</span>
                  </div>
                  <div className="signal-item">
                    <div className="signal-left">
                      <div className="signal-icon buy">↗</div>
                      <div className="signal-info">
                        <span className="signal-pair">SOL/USDT</span>
                        <span className="signal-type">Buy Signal</span>
                      </div>
                    </div>
                    <span className="signal-confidence">89%</span>
                  </div>
                  <div className="signal-item">
                    <div className="signal-left">
                      <div className="signal-icon hold">→</div>
                      <div className="signal-info">
                        <span className="signal-pair">ADA/USDT</span>
                        <span className="signal-type">Hold</span>
                      </div>
                    </div>
                    <span className="signal-confidence">92%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="pricing-section" id="pricing">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">
              <span className="title-main">Choose Your Plan</span>
              <span className="title-sub"></span>
            </h2>
            <p className="section-description">
              Flexible pricing options designed to scale with your trading success.
            </p>
          </div>

          <div className="pricing-grid">
            <div className="pricing-card">
              <div className="plan-header">
                <h3 className="plan-name">Starter</h3>
                <div className="plan-price">
                  <span className="price-amount">$29</span>
                  <span className="price-period">/month</span>
                </div>
                <p className="plan-description">Perfect for beginners getting started with automated trading.</p>
              </div>
              <div className="plan-features">
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>3 Trading Bots</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Basic Analytics</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Email Support</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>$10K Trading Limit</span>
                </div>
              </div>
              <button className="plan-button" onClick={openApp}>Get Started</button>
            </div>

            <div className="pricing-card featured">
              <div className="plan-badge">Most Popular</div>
              <div className="plan-header">
                <h3 className="plan-name">Professional</h3>
                <div className="plan-price">
                  <span className="price-amount">$99</span>
                  <span className="price-period">/month</span>
                </div>
                <p className="plan-description">Advanced features for serious traders and professionals.</p>
              </div>
              <div className="plan-features">
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>15 Trading Bots</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Advanced Analytics</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Priority Support</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>$100K Trading Limit</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Custom Strategies</span>
                </div>
              </div>
              <button className="plan-button primary" onClick={openApp}>Start Free Trial</button>
            </div>

            <div className="pricing-card">
              <div className="plan-header">
                <h3 className="plan-name">Enterprise</h3>
                <div className="plan-price">
                  <span className="price-amount">$299</span>
                  <span className="price-period">/month</span>
                </div>
                <p className="plan-description">Complete solution for institutions and high-volume traders.</p>
              </div>
              <div className="plan-features">
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Unlimited Bots</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Full Analytics Suite</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>24/7 Phone Support</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>Unlimited Trading</span>
                </div>
                <div className="feature-item">
                  <svg className="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>API Access</span>
                </div>
              </div>
              <button className="plan-button" onClick={openApp}>Contact Sales</button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer-section" id="contact">
        <div className="section-container">
          <div className="footer-content">
            <div className="footer-brand">
              <div className="nav-brand">
                <div className="brand-icon">
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="url(#brandGradientFooter)" stroke="currentColor" strokeWidth="1"/>
                    <defs>
                      <linearGradient id="brandGradientFooter" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style={{stopColor:'#3b82f6'}}/>
                        <stop offset="100%" style={{stopColor:'#14b8a6'}}/>
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                <span className="brand-text">Kamikaze</span>
                <span className="brand-badge">PRO</span>
              </div>
              <p className="footer-description">
                The future of cryptocurrency trading is here. Join thousands of successful traders using our AI-powered platform.
              </p>
            </div>

            <div className="footer-links">
              <div className="link-group">
                <h4 className="link-title">Product</h4>
                <a href="#features" className="footer-link">Features</a>
                <a href="#analytics" className="footer-link">Analytics</a>
                <a href="#pricing" className="footer-link">Pricing</a>
                <a href="#" className="footer-link">API</a>
              </div>

              <div className="link-group">
                <h4 className="link-title">Company</h4>
                <a href="#about" className="footer-link">About</a>
                <a href="#" className="footer-link">Careers</a>
                <a href="#" className="footer-link">Blog</a>
                <a href="#contact" className="footer-link">Contact</a>
              </div>

              <div className="link-group">
                <h4 className="link-title">Support</h4>
                <a href="#" className="footer-link">Help Center</a>
                <a href="#" className="footer-link">Documentation</a>
                <a href="#" className="footer-link">Community</a>
                <a href="#" className="footer-link">Status</a>
              </div>

              <div className="link-group">
                <h4 className="link-title">Legal</h4>
                <a href="#" className="footer-link">Privacy Policy</a>
                <a href="#" className="footer-link">Terms of Service</a>
                <a href="#" className="footer-link">Cookie Policy</a>
                <a href="#" className="footer-link">Disclaimer</a>
              </div>
            </div>
          </div>

          <div className="footer-bottom">
            <p className="copyright">© {new Date().getFullYear()} Kamikaze AI. All rights reserved.</p>
            <div className="social-links">
              <a href="#" className="social-link" aria-label="Twitter">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0 0 20 3.92a8.19 8.19 0 0 1-2.357.646 4.118 4.118 0 0 0 1.804-2.27 8.224 8.224 0 0 1-2.605.996 4.107 4.107 0 0 0-6.993 3.743 11.65 11.65 0 0 1-8.457-4.287 4.106 4.106 0 0 0 1.27 5.477A4.073 4.073 0 0 1 .8 7.713v.052a4.105 4.105 0 0 0 3.292 4.022 4.095 4.095 0 0 1-1.853.07 4.108 4.108 0 0 0 3.834 2.85A8.233 8.233 0 0 1 0 16.407a11.616 11.616 0 0 0 6.29 1.84" fill="currentColor"/>
                </svg>
              </a>
              <a href="#" className="social-link" aria-label="Discord">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M16.942 4.556a16.3 16.3 0 0 0-4.126-1.3 12.04 12.04 0 0 0-.529 1.1 15.175 15.175 0 0 0-4.573 0 11.585 11.585 0 0 0-.535-1.1 16.274 16.274 0 0 0-4.129 1.3A17.392 17.392 0 0 0 .182 13.218a15.785 15.785 0 0 0 4.963 2.521c.41-.564.773-1.16 1.084-1.785a10.63 10.63 0 0 1-1.706-.83c.143-.106.283-.217.418-.33a11.664 11.664 0 0 0 10.118 0c.137.113.277.224.418.33-.544.328-1.116.606-1.71.832a12.52 12.52 0 0 0 1.084 1.785 16.46 16.46 0 0 0 5.064-2.595 17.286 17.286 0 0 0-2.973-8.662ZM6.678 10.813a1.941 1.941 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.919 1.919 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Zm6.644 0a1.94 1.94 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.918 1.918 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Z" fill="currentColor"/>
                </svg>
              </a>
              <a href="#" className="social-link" aria-label="Telegram">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0Zm4.822 6.862-1.687 7.964c-.127.565-.459.703-.93.438l-2.565-1.89-1.238 1.192c-.137.137-.252.252-.516.252l.184-2.607 4.74-4.284c.206-.184-.045-.286-.32-.102L8.732 10.73l-2.566-.802c-.558-.175-.568-.558.116-.825l10.03-3.867c.465-.175.872.102.722.825Z" fill="currentColor"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* Authentication Modals */}
      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
        onSwitchToRegister={() => {
          setIsSignInModalOpen(false);
          setIsRegisterModalOpen(true);
        }}
      />

      <RegisterModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
        onSwitchToSignIn={() => {
          setIsRegisterModalOpen(false);
          setIsSignInModalOpen(true);
        }}
      />
    </div>
  );
};

export default Landing;
