import React from 'react';
import { DashboardGrid } from "@/components/DashboardGrid";
import { QuickStartTrading } from "@/components/agents/QuickStartTrading";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useUser } from "@/hooks/useUser";
import { useExchange } from "@/contexts/ExchangeContext";
import { useDashboardData, usePortfolioValue, useTradingBots } from "@/hooks/useDashboardData";

import {
  Zap,
  TrendingUp,
  Settings,
  RefreshCw,
  Bell,
  Download,
  Wifi,
  AlertCircle,
  Play,
  Pause,
  Activity,
  WifiOff
} from 'lucide-react';

const Dashboard = () => {
  const { user } = useUser();
  const { hasConnectedExchange, setShowConnectionModal } = useExchange();
  const {
    loading,
    error,
    refreshDashboard,
    overview,
    isRealTimeActive,
    connectionStatus,
    toggleRealTimeUpdates,
    lastUpdated
  } = useDashboardData();
  const { totalValueUsd, dailyPnl, dailyPnlPercent } = usePortfolioValue();
  const { activeBotCount, totalBots, averageWinRate } = useTradingBots();

  // Check if user has connected exchange based on both local state and API response
  const isExchangeConnected = hasConnectedExchange() && overview?.environment !== 'disconnected';

  const currentTime = new Date().toLocaleString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  const firstName = user?.name?.split(' ')[0] || 'there';

  // Real-time status indicator
  const getRealTimeStatusBadge = () => {
    if (!isRealTimeActive) {
      return (
        <Badge className="bg-muted/10 text-muted-foreground border-muted/30 flex items-center gap-1">
          <Pause className="h-3 w-3" />
          Paused
        </Badge>
      );
    }

    switch (connectionStatus) {
      case 'connected':
        return (
          <Badge className="bg-success/10 text-success border-success/30 flex items-center gap-1">
            <Activity className="h-3 w-3 animate-pulse" />
            Live Updates
          </Badge>
        );
      case 'connecting':
        return (
          <Badge className="bg-warning/10 text-warning border-warning/30 flex items-center gap-1">
            <RefreshCw className="h-3 w-3 animate-spin" />
            Connecting
          </Badge>
        );
      case 'error':
        return (
          <Badge className="bg-destructive/10 text-destructive border-destructive/30 flex items-center gap-1">
            <WifiOff className="h-3 w-3" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge className="bg-muted/10 text-muted-foreground border-muted/30 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Disconnected
          </Badge>
        );
    }
  };

  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never';
    const now = Date.now();
    const diff = now - lastUpdated;

    if (diff < 1000) return 'Just now';
    if (diff < 60000) return `${Math.floor(diff / 1000)}s ago`;
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    return new Date(lastUpdated).toLocaleTimeString();
  };

  return (
    <div className="space-y-8">
      {/* Error Alert */}
      {error && (
        <Alert className="border-destructive/20 bg-gradient-to-r from-destructive/5 via-destructive/5 to-destructive/5 backdrop-blur-sm">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-destructive/10 backdrop-blur-sm">
                <AlertCircle className="h-5 w-5 text-destructive" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground mb-1">Connection Error</h4>
                <p className="text-sm text-muted-foreground">
                  {error}
                </p>
              </div>
            </div>
            <Button
              onClick={refreshDashboard}
              variant="outline"
              size="sm"
              className="ml-6"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </Alert>
      )}

      {/* Exchange Connection Alert */}
      {!isExchangeConnected && !error && (
        <Alert className="border-primary/20 bg-gradient-to-r from-primary/5 via-accent-teal/5 to-primary/5 backdrop-blur-sm">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10 backdrop-blur-sm">
                <AlertCircle className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground mb-1">Connect Your Exchange</h4>
                <p className="text-sm text-muted-foreground">
                  Connect your exchange account to unlock real-time trading with FluxTrader
                </p>
              </div>
            </div>
            <Button
              onClick={() => setShowConnectionModal(true)}
              className="ml-6 bg-gradient-to-r from-yellow-400 to-amber-500 hover:from-yellow-300 hover:to-amber-400 hover:bg-gradient-to-r text-black hover:text-black font-bold px-6 py-2.5 rounded-lg shadow-lg shadow-yellow-500/30 hover:shadow-xl hover:shadow-yellow-400/40 transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 group relative overflow-hidden border border-yellow-400/60 hover:border-yellow-300/70 backdrop-blur-sm focus:ring-2 focus:ring-yellow-400/50 focus:outline-none"
              style={{
                background: 'linear-gradient(to right, rgb(250 204 21), rgb(245 158 11))',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, rgb(253 224 71), rgb(251 191 36))';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, rgb(250 204 21), rgb(245 158 11))';
              }}
            >
              <div className="relative flex items-center gap-2">
                <Wifi className="h-4 w-4 transition-transform duration-300 group-hover:rotate-12" />
                <span>Connect Now</span>
                <div className="w-1 h-1 rounded-full bg-black/60 animate-pulse"></div>
              </div>
            </Button>
          </div>
        </Alert>
      )}

      {/* Premium Header */}
      <div className="glass-card hover:shadow-elevated transition-all duration-300 p-6 border border-card-border/60">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold text-foreground">
                Kamikaze Trader
              </h1>
              {isExchangeConnected ? (
                <div className="flex items-center gap-2">
                  <Badge className="bg-success/10 text-success border-success/30 flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    Exchange Connected
                  </Badge>
                  {getRealTimeStatusBadge()}
                </div>
              ) : (
                <Badge className="bg-muted/10 text-muted-foreground border-muted/30 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Not Connected
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              Welcome back, {firstName}! Your AI-powered trading dashboard is ready.
            </p>
            <p className="text-sm text-muted-foreground">
              Last updated: {getLastUpdatedText()} • System time: {currentTime}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <QuickStartTrading />
            <Button variant="ghost" size="sm">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshDashboard}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant={isRealTimeActive ? "default" : "outline"}
              size="sm"
              onClick={toggleRealTimeUpdates}
              className={isRealTimeActive ? "bg-success hover:bg-success/90" : ""}
            >
              {isRealTimeActive ? (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Pause Live
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Live
                </>
              )}
            </Button>
            <Button variant="default" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Quick Stats Bar */}
        <div className="mt-6 pt-6 border-t border-card-border/30">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className={`text-2xl font-bold ${dailyPnl >= 0 ? 'text-success' : 'text-destructive'}`}>
                {dailyPnl >= 0 ? '+' : ''}${dailyPnl.toFixed(5)}
              </p>
              <p className="text-xs text-muted-foreground">Today's P&L</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-foreground">{activeBotCount}/{totalBots}</p>
              <p className="text-xs text-muted-foreground">Active Bots</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{Math.round(averageWinRate)}%</p>
              <p className="text-xs text-muted-foreground">Win Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-warning">0</p>
              <p className="text-xs text-muted-foreground">Trades Today</p>
            </div>
          </div>
        </div>
      </div>



      {/* Dashboard Grid */}
      <DashboardGrid />
    </div>
  );
};

export default Dashboard;