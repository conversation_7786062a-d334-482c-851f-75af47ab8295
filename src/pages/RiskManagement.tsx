import { Shield } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const RiskManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Risk Management</h1>
          <p className="text-muted-foreground">Comprehensive risk control and portfolio protection</p>
        </div>
      </div>

      <Card className="bg-card shadow-card border-border">
        <CardContent className="p-12 text-center">
          <Shield className="w-24 h-24 text-primary mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-foreground mb-4">Advanced Risk Management</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Protect your investments with sophisticated risk management tools including stop-losses, 
            position sizing, correlation analysis, and portfolio risk assessment.
          </p>
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20 text-lg px-4 py-2">
            Coming Soon
          </Badge>
        </CardContent>
      </Card>
    </div>
  );
};

export default RiskManagement;