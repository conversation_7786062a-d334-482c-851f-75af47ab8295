import { <PERSON>sp<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Percent, BarChart3 } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AssetAllocation } from "@/components/dashboard/AssetAllocation";

const Portfolio = () => {
  const portfolioData = [
    { name: 'Bitcoin', value: 45, amount: 56439.23, changePercent: 2.34 },
    { name: 'Ethereum', value: 30, amount: 37626.15, changePercent: 1.87 },
    { name: 'Cardano', value: 15, amount: 18813.08, changePercent: -0.45 },
    { name: 'Others', value: 10, amount: 12541.05, changePercent: 0.12 }
  ];

  const performanceData = [
    { period: 'Jan', value: 110000, profit: 8500 },
    { period: 'Feb', value: 115000, profit: 5000 },
    { period: 'Mar', value: 108000, profit: -7000 },
    { period: 'Apr', value: 122000, profit: 14000 },
    { period: 'May', value: 125420, profit: 3420 },
  ];

  const holdings = [
    { 
      symbol: 'BTC', 
      name: 'Bitcoin', 
      amount: 1.2847, 
      value: 56439.23, 
      price: 43950.50, 
      change: 2.34, 
      allocation: 45 
    },
    { 
      symbol: 'ETH', 
      name: 'Ethereum', 
      amount: 13.254, 
      value: 37626.15, 
      price: 2840.75, 
      change: 1.87, 
      allocation: 30 
    },
    { 
      symbol: 'ADA', 
      name: 'Cardano', 
      amount: 38947.2, 
      value: 18813.08, 
      price: 0.483, 
      change: -0.45, 
      allocation: 15 
    },
    { 
      symbol: 'SOL', 
      name: 'Solana', 
      amount: 89.34, 
      value: 9124.56, 
      price: 102.15, 
      change: 3.21, 
      allocation: 7 
    },
    { 
      symbol: 'DOT', 
      name: 'Polkadot', 
      amount: 503.2, 
      value: 3416.49, 
      price: 6.79, 
      change: -1.23, 
      allocation: 3 
    }
  ];

  const totalValue = portfolioData.reduce((sum, item) => sum + item.amount, 0);
  const totalChange = 2847.35;
  const changePercent = 2.32;



  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Portfolio</h1>
        <p className="text-muted-foreground">Track your investment performance and holdings</p>
      </div>

      {/* Portfolio Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <Card className="lg:col-span-1 glass-card hover:shadow-elevated transition-all duration-300">
          <CardContent className="p-6">
            <div className="text-center space-y-2">
              <DollarSign className="w-8 h-8 text-primary mx-auto" />
              <div className="text-2xl font-bold text-foreground">
                ${totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </div>
              <div className="text-sm text-muted-foreground">Total Portfolio Value</div>
              <div className={`flex items-center justify-center gap-1 text-sm ${totalChange > 0 ? 'text-success' : 'text-destructive'}`}>
                {totalChange > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {totalChange > 0 ? '+' : ''}${Math.abs(totalChange).toLocaleString()} ({changePercent > 0 ? '+' : ''}{changePercent}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="lg:col-span-3">
          <AssetAllocation
            title="Asset Allocation"
            data={portfolioData}
            height={320}
            className="h-full"
          />
        </div>
      </div>

      <Tabs defaultValue="holdings" className="space-y-6">
        <TabsList className="bg-card border border-border">
          <TabsTrigger value="holdings" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Holdings
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Performance
          </TabsTrigger>
          <TabsTrigger value="transactions" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Transactions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="holdings" className="space-y-6">
          <Card className="bg-card shadow-card border-border">
            <CardHeader>
              <CardTitle className="text-lg text-foreground">Current Holdings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {holdings.map((holding, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-secondary/50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold">
                        {holding.symbol}
                      </div>
                      <div>
                        <div className="font-semibold text-foreground">{holding.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {holding.amount.toLocaleString()} {holding.symbol}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-semibold text-foreground">
                        ${holding.value.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ${holding.price.toLocaleString()} per {holding.symbol}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`font-semibold ${holding.change > 0 ? 'text-profit' : 'text-loss'}`}>
                        {holding.change > 0 ? '+' : ''}{holding.change}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {holding.allocation}% allocation
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card className="bg-card shadow-card border-border">
            <CardHeader>
              <CardTitle className="text-lg text-foreground">Portfolio Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis 
                      dataKey="period" 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />
                    <YAxis 
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                      tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                        color: 'hsl(var(--foreground))'
                      }}
                      formatter={(value, name) => [
                        name === 'value' ? `$${value.toLocaleString()}` : `$${value.toLocaleString()}`,
                        name === 'value' ? 'Portfolio Value' : 'Monthly P&L'
                      ]}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={3}
                      dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="profit" 
                      stroke="hsl(var(--chart-2))" 
                      strokeWidth={2}
                      dot={{ fill: 'hsl(var(--chart-2))', strokeWidth: 2, r: 3 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card className="bg-card shadow-card border-border">
            <CardContent className="p-6 text-center">
              <BarChart3 className="w-16 h-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Transaction History</h3>
              <p className="text-muted-foreground mb-6">View all your trading transactions and portfolio changes</p>
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                Coming Soon
              </Badge>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Portfolio;