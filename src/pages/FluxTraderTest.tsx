import React from 'react';
import { useParams } from 'react-router-dom';
import { appConfig } from '../config/appConfig';

const FluxTraderTest: React.FC = () => {
  const { agentId } = useParams<{ agentId: string }>();

  const handleFetchStatus = async () => {
    if (agentId) {
      try {
        const response = await fetch(`${appConfig.API_BASE_URL}/api/v1/agents/${agentId}/status`);
        const data = await response.json();
        if (response.ok) {
          console.log('API Response:', data);
          alert('API Response: ' + JSON.stringify(data, null, 2));
        } else {
          alert('API request failed');
        }
      } catch (error) {
        console.error('API Error:', error);
        alert('API Error: ' + error);
      }
    }
  };

  return (
    <div style={{
      padding: '20px',
      backgroundColor: 'white',
      color: 'black',
      minHeight: '100vh',
      fontSize: '18px'
    }}>
      <h1 style={{ color: 'red', fontSize: '24px' }}>FluxTrader Dashboard Test</h1>
      <p><strong>✅ Component is rendering successfully!</strong></p>
      <p><strong>Agent ID from URL:</strong> {agentId}</p>
      <p><strong>Full URL:</strong> {window.location.href}</p>
      <p><strong>Pathname:</strong> {window.location.pathname}</p>

      <div style={{ marginTop: '20px', padding: '10px', border: '1px solid black' }}>
        <h2>API Test</h2>
        <button
          onClick={handleFetchStatus}
          style={{ padding: '10px', fontSize: '16px' }}
        >
          Test API Call
        </button>
      </div>
    </div>
  );
};

export default FluxTraderTest;