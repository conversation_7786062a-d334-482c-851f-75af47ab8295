import { Settings as SettingsIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Settings = () => {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Settings</h1>
        <p className="text-muted-foreground">Configure your trading preferences and system settings</p>
      </div>

      <Card className="glass-card hover:shadow-elevated transition-all duration-300">
        <CardContent className="p-12 text-center">
          <SettingsIcon className="w-24 h-24 text-primary mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-foreground mb-4">System Settings</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Customize your trading environment with preferences for notifications, display settings,
            trading parameters, security options, and integration configurations.
          </p>
          <Badge variant="default" className="text-lg px-4 py-2">
            Coming Soon
          </Badge>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;