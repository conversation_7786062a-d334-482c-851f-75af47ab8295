import { Clock, TrendingUp, TrendingDown, Filter, Download, Search } from "lucide-react";
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const TradingHistory = () => {
  const trades = [
    {
      id: "T001",
      pair: "BTC/USDT",
      type: "BUY",
      amount: 0.0245,
      price: 44890.50,
      total: 1099.82,
      profit: 127.45,
      profitPercent: 13.1,
      time: "2024-01-15 14:32:15",
      bot: "Conservative Growth",
      status: "completed",
      fee: 1.65
    },
    {
      id: "T002",
      pair: "ETH/USDT",
      type: "SELL",
      amount: 1.5,
      price: 2875.30,
      total: 4312.95,
      profit: 243.80,
      profitPercent: 6.0,
      time: "2024-01-15 13:45:22",
      bot: "Aggressive Scalper",
      status: "completed",
      fee: 6.47
    },
    {
      id: "T003",
      pair: "ADA/USDT",
      type: "BUY",
      amount: 1250,
      price: 0.478,
      total: 597.50,
      profit: -15.25,
      profitPercent: -2.5,
      time: "2024-01-15 12:18:44",
      bot: "Balanced Trader",
      status: "completed",
      fee: 0.90
    },
    {
      id: "T004",
      pair: "BTC/USDT",
      type: "SELL",
      amount: 0.0189,
      price: 45120.00,
      total: 852.77,
      profit: 89.30,
      profitPercent: 11.7,
      time: "2024-01-15 11:55:12",
      bot: "Conservative Growth",
      status: "completed",
      fee: 1.28
    },
    {
      id: "T005",
      pair: "SOL/USDT",
      type: "BUY",
      amount: 12.5,
      price: 102.35,
      total: 1279.38,
      profit: 45.67,
      profitPercent: 3.7,
      time: "2024-01-15 10:22:33",
      bot: "Aggressive Scalper",
      status: "completed",
      fee: 1.92
    },
    {
      id: "T006",
      pair: "ETH/USDT",
      type: "SELL",
      amount: 0.85,
      price: 2890.75,
      total: 2457.14,
      profit: 156.89,
      profitPercent: 6.8,
      time: "2024-01-15 09:14:56",
      bot: "Balanced Trader",
      status: "completed",
      fee: 3.69
    }
  ];

  const orders = [
    {
      id: "O001",
      pair: "BTC/USDT",
      type: "LIMIT_BUY",
      amount: 0.025,
      price: 44500.00,
      total: 1112.50,
      time: "2024-01-15 15:45:22",
      status: "pending",
      bot: "Conservative Growth"
    },
    {
      id: "O002",
      pair: "ETH/USDT",
      type: "STOP_LOSS",
      amount: 1.2,
      price: 2750.00,
      total: 3300.00,
      time: "2024-01-15 15:30:15",
      status: "active",
      bot: "Risk Manager"
    }
  ];

  const getTypeColor = (type: string) => {
    if (type.includes("BUY")) return "text-profit bg-profit/10 border-profit/20";
    if (type.includes("SELL")) return "text-destructive bg-destructive/10 border-destructive/20";
    return "text-primary bg-primary/10 border-primary/20";
  };

  const getProfitColor = (profit: number) => {
    if (profit > 0) return "text-profit";
    if (profit < 0) return "text-loss";
    return "text-muted-foreground";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-profit bg-profit/10 border-profit/20";
      case "pending": return "text-warning bg-warning/10 border-warning/20";
      case "active": return "text-primary bg-primary/10 border-primary/20";
      case "cancelled": return "text-muted-foreground bg-muted/10 border-muted/20";
      default: return "text-foreground bg-secondary border-border";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Trading History</h1>
          <p className="text-muted-foreground">View and analyze your trading activity</p>
        </div>
        <Button variant="outline" className="border-border text-foreground">
          <Download className="w-4 h-4 mr-2" />
          Export
        </Button>
      </div>

      {/* Filters */}
      <Card className="bg-card shadow-card border-border">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="relative flex-1 min-w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input 
                placeholder="Search by pair, bot, or transaction ID..." 
                className="pl-10 bg-secondary border-border focus:ring-primary"
              />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-32 bg-secondary border-border">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="buy">Buy</SelectItem>
                <SelectItem value="sell">Sell</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-32 bg-secondary border-border">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="7d">
              <SelectTrigger className="w-32 bg-secondary border-border">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="1d">1 Day</SelectItem>
                <SelectItem value="7d">7 Days</SelectItem>
                <SelectItem value="30d">30 Days</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="trades" className="space-y-6">
        <TabsList className="bg-card border border-border">
          <TabsTrigger value="trades" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Completed Trades ({trades.length})
          </TabsTrigger>
          <TabsTrigger value="orders" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Open Orders ({orders.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="trades" className="space-y-4">
          {trades.map((trade) => (
            <Card key={trade.id} className="bg-card shadow-card border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary" className={getTypeColor(trade.type)}>
                      {trade.type}
                    </Badge>
                    <div>
                      <div className="font-semibold text-foreground">{trade.pair}</div>
                      <div className="text-sm text-muted-foreground">ID: {trade.id}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">{trade.time}</div>
                    <Badge variant="secondary" className={getStatusColor(trade.status)}>
                      {trade.status}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">Amount</div>
                    <div className="font-medium text-foreground">
                      {trade.amount.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Price</div>
                    <div className="font-medium text-foreground">
                      ${trade.price.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Total</div>
                    <div className="font-medium text-foreground">
                      ${trade.total.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">P&L</div>
                    <div className={`font-medium flex items-center gap-1 ${getProfitColor(trade.profit)}`}>
                      {trade.profit > 0 ? (
                        <TrendingUp className="w-3 h-3" />
                      ) : trade.profit < 0 ? (
                        <TrendingDown className="w-3 h-3" />
                      ) : null}
                      {trade.profit > 0 ? '+' : ''}${trade.profit.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">P&L %</div>
                    <div className={`font-medium ${getProfitColor(trade.profit)}`}>
                      {trade.profitPercent > 0 ? '+' : ''}{trade.profitPercent}%
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Bot</div>
                    <div className="font-medium text-foreground truncate">
                      {trade.bot}
                    </div>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-border flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Fee: ${trade.fee.toLocaleString()}
                  </div>
                  <Button size="sm" variant="outline" className="border-border text-foreground">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          {orders.map((order) => (
            <Card key={order.id} className="bg-card shadow-card border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary" className={getTypeColor(order.type)}>
                      {order.type.replace('_', ' ')}
                    </Badge>
                    <div>
                      <div className="font-semibold text-foreground">{order.pair}</div>
                      <div className="text-sm text-muted-foreground">ID: {order.id}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">{order.time}</div>
                    <Badge variant="secondary" className={getStatusColor(order.status)}>
                      {order.status}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">Amount</div>
                    <div className="font-medium text-foreground">
                      {order.amount.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Price</div>
                    <div className="font-medium text-foreground">
                      ${order.price.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Total</div>
                    <div className="font-medium text-foreground">
                      ${order.total.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Bot</div>
                    <div className="font-medium text-foreground">
                      {order.bot}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="border-border text-foreground">
                      Cancel
                    </Button>
                    <Button size="sm" variant="outline" className="border-border text-foreground">
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Trading Analytics Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="bg-card shadow-card border-border">
              <CardContent className="p-6 text-center">
                <Clock className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">127</div>
                <div className="text-sm text-muted-foreground">Total Trades</div>
                <div className="text-sm text-profit">+15 this week</div>
              </CardContent>
            </Card>
            
            <Card className="bg-card shadow-card border-border">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 text-profit mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">72%</div>
                <div className="text-sm text-muted-foreground">Win Rate</div>
                <div className="text-sm text-profit">+3% vs last month</div>
              </CardContent>
            </Card>
            
            <Card className="bg-card shadow-card border-border">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 text-profit mx-auto mb-2" />
                <div className="text-2xl font-bold text-profit">+$4,260</div>
                <div className="text-sm text-muted-foreground">Total P&L</div>
                <div className="text-sm text-profit">+18.7% ROI</div>
              </CardContent>
            </Card>
            
            <Card className="bg-card shadow-card border-border">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-foreground">4.2h</div>
                <div className="text-sm text-muted-foreground">Avg Hold Time</div>
                <div className="text-sm text-muted-foreground">Optimal range</div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-card shadow-card border-border">
            <CardContent className="p-6 text-center">
              <Clock className="w-16 h-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Advanced Analytics</h3>
              <p className="text-muted-foreground mb-6">Detailed performance metrics and trading insights coming soon</p>
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                Coming Soon
              </Badge>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TradingHistory;