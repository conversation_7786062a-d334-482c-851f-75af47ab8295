/* Kamikaze Trading Pro - Landing Page Styles */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Landing Page Specific Variables - Override global variables within landing page scope */
.landing-page {
    /* Override global CSS variables with dark theme values for landing page */
    --background: hsl(0, 0%, 3%);
    --foreground: hsl(210, 40%, 98%);
    --primary: hsl(217, 91%, 60%);
    --primary-foreground: hsl(210, 40%, 98%);
    --secondary: hsl(215, 28%, 22%);
    --secondary-foreground: hsl(210, 20%, 88%);
    --accent-teal: hsl(178, 60%, 48%);
    --accent-purple: hsl(262, 83%, 58%);
    --accent-orange: hsl(24, 95%, 53%);
    --success: hsl(160, 84%, 39%);
    --warning: hsl(32, 95%, 44%);
    --destructive: hsl(0, 84%, 60%);
    --border: hsl(220, 13%, 35%, 0.4);
    --card: hsl(220, 13%, 25%);
    --card-foreground: hsl(210, 40%, 98%);
    --muted: hsl(215, 28%, 18%);
    --muted-foreground: hsl(215, 16%, 70%);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(217, 91%, 70%) 50%, hsl(217, 91%, 55%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(178, 60%, 48%) 100%);
    --gradient-glass: linear-gradient(135deg, hsl(220, 13%, 25%, 0.9) 0%, hsl(220, 13%, 30%, 0.85) 50%, hsl(220, 13%, 22%, 0.9) 100%);

    /* Shadows */
    --shadow-glass: 0 8px 32px hsl(222, 84%, 3%, 0.3), 0 4px 16px hsl(222, 84%, 3%, 0.2), inset 0 1px 0 hsl(220, 13%, 35%, 0.15);
    --shadow-elevated: 0 20px 64px hsl(222, 84%, 3%, 0.4), 0 12px 32px hsl(222, 84%, 3%, 0.3);
    --shadow-glow-primary: 0 0 32px hsl(217, 91%, 60%, 0.3), 0 0 64px hsl(217, 91%, 60%, 0.1);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 6rem 0;
    --border-radius: 0.875rem;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

/* Landing Page Wrapper */
.landing-page {
    font-family: var(--font-family);
    background: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    overflow-x: hidden;
    background-image:
        radial-gradient(circle at 20% 80%, hsl(217, 91%, 60%, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsl(178, 60%, 48%, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, hsl(262, 83%, 58%, 0.04) 0%, transparent 50%);
    min-height: 100vh;
}

/* Ensure body inherits landing page styles when landing page is active */
body:has(.landing-page) {
    font-family: var(--font-family);
    background: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    overflow-x: hidden;
    background-image:
        radial-gradient(circle at 20% 80%, hsl(217, 91%, 60%, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsl(178, 60%, 48%, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, hsl(262, 83%, 58%, 0.04) 0%, transparent 50%);
    min-height: 100vh;
}

/* Glass Morphism Effects */
.glass-card {
    background: var(--gradient-glass);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    box-shadow: var(--shadow-glass);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.1) 0%, transparent 50%, hsl(217, 91%, 60%, 0.05) 100%);
    opacity: 0.5;
    pointer-events: none;
    border-radius: inherit;
}

/* Navigation Styles */
.header-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: hsl(var(--background) / 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.brand-icon {
    color: var(--primary);
    display: flex;
    align-items: center;
}

.brand-text {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 800;
}

.brand-badge {
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--muted-foreground);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--foreground);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-link:hover::after {
    transform: scaleX(1);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 0.25rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 1.5rem;
    height: 2px;
    background: var(--foreground);
    transition: all 0.3s ease;
}

/* Button Styles */
.btn-primary, .btn-secondary, .btn-hero-primary, .btn-hero-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn-primary, .btn-hero-primary {
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    box-shadow: 0 8px 24px hsl(217, 91%, 60%, 0.3);
}

.btn-primary:hover, .btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px hsl(217, 91%, 60%, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--foreground);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--card);
    border-color: var(--primary);
}

.btn-hero-primary {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-hero-secondary {
    background: transparent;
    color: var(--foreground);
    border: 1px solid var(--border);
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-hero-secondary:hover {
    background: var(--card);
    border-color: var(--primary);
}

/* Hero Section - Enhanced */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 8rem 0 6rem;
}

.hero-background {
    position: absolute;
    inset: 0;
    z-index: 1;
}

.hero-gradient-1 {
    position: absolute;
    top: 20%;
    left: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, hsl(217, 91%, 60%, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.hero-gradient-2 {
    position: absolute;
    top: 60%;
    right: 15%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, hsl(178, 60%, 48%, 0.12) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite reverse;
}

.hero-gradient-3 {
    position: absolute;
    bottom: 10%;
    left: 50%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, hsl(262, 83%, 58%, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 7s ease-in-out infinite;
    transform: translateX(-50%);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}



/* Enhanced Hero Title */
.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    letter-spacing: -0.03em;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.title-line-1, .title-line-3 {
    color: var(--foreground);
}

.title-line-2 {
    position: relative;
}

.gradient-text-enhanced {
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(178, 60%, 48%) 50%, hsl(262, 83%, 58%) 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 3s ease-in-out infinite;
    position: relative;
}

.gradient-text-enhanced::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.2) 0%, hsl(178, 60%, 48%, 0.2) 50%, hsl(262, 83%, 58%, 0.2) 100%);
    filter: blur(20px);
    z-index: -1;
}

/* Enhanced Hero Description */
.hero-description {
    font-size: 1.25rem;
    color: var(--muted-foreground);
    line-height: 1.7;
    max-width: 500px;
}

/* Enhanced Stats Container */
.hero-stats-container {
    margin: 2rem 0;
}

.stats-grid {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: var(--gradient-glass);
    border: 1px solid var(--border);
    border-radius: 1rem;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    flex: 1;
    max-width: 280px;
}

.stat-item::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-4px);
    border-color: hsl(217, 91%, 60%, 0.3);
    box-shadow: 0 12px 32px hsl(217, 91%, 60%, 0.15);
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 0.75rem;
    color: var(--primary-foreground);
    flex-shrink: 0;
}

.stat-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--foreground);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

/* Enhanced Hero Actions */
.hero-actions {
    display: flex;
    justify-content: flex-start;
    margin: 2rem 0;
}

.btn-hero-primary {
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.125rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.25rem 2.5rem;
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    box-shadow: 0 12px 32px hsl(217, 91%, 60%, 0.4);
}

.btn-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 2;
}

.btn-glow {
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent, hsl(217, 91%, 60%, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 16px 40px hsl(217, 91%, 60%, 0.5);
}

.btn-hero-primary:hover .btn-glow {
    opacity: 1;
}

/* Enhanced Hero Visual */
.hero-visual {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.visual-background {
    position: absolute;
    inset: 0;
    z-index: 1;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
}

.element-1 {
    top: 10%;
    right: 20%;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.2), hsl(178, 60%, 48%, 0.2));
    animation: float 6s ease-in-out infinite;
}

.element-2 {
    bottom: 20%;
    left: 10%;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, hsl(262, 83%, 58%, 0.2), hsl(217, 91%, 60%, 0.2));
    animation: float 8s ease-in-out infinite reverse;
}

.element-3 {
    top: 50%;
    left: 5%;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, hsl(178, 60%, 48%, 0.2), hsl(262, 83%, 58%, 0.2));
    animation: float 7s ease-in-out infinite;
}

.dashboard-mockup {
    background: var(--gradient-glass);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 1.5rem;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 25px 50px hsl(217, 91%, 60%, 0.15),
        0 12px 25px hsl(0, 0%, 0%, 0.3),
        inset 0 1px 0 hsl(255, 255%, 255%, 0.1);
    overflow: hidden;
    transform: perspective(1200px) rotateY(-8deg) rotateX(4deg);
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    max-width: 600px;
    width: 100%;
}

.mockup-glow {
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.3), hsl(178, 60%, 48%, 0.2), hsl(262, 83%, 58%, 0.3));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.dashboard-mockup:hover {
    transform: perspective(1200px) rotateY(-4deg) rotateX(2deg) scale(1.02);
    box-shadow:
        0 35px 70px hsl(217, 91%, 60%, 0.2),
        0 15px 35px hsl(0, 0%, 0%, 0.4),
        inset 0 1px 0 hsl(255, 255%, 255%, 0.15);
}

.dashboard-mockup:hover .mockup-glow {
    opacity: 1;
}

.mockup-header {
    background: linear-gradient(135deg, hsl(220, 13%, 20%) 0%, hsl(220, 13%, 25%) 100%);
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.2);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mockup-controls {
    display: flex;
    gap: 0.75rem;
}

.control-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    position: relative;
}

.control-dot.red {
    background: linear-gradient(135deg, #ff5f57, #ff3b30);
    box-shadow: 0 2px 4px rgba(255, 59, 48, 0.3);
}

.control-dot.yellow {
    background: linear-gradient(135deg, #ffbd2e, #ff9500);
    box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
}

.control-dot.green {
    background: linear-gradient(135deg, #28ca42, #30d158);
    box-shadow: 0 2px 4px rgba(48, 209, 88, 0.3);
}

.mockup-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 600;
    color: var(--foreground);
    font-size: 0.95rem;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: hsl(var(--success), 0.1);
    border: 1px solid hsl(var(--success), 0.3);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--success);
}

.live-dot {
    width: 6px;
    height: 6px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.mockup-content {
    display: flex;
    height: 450px;
}

.mockup-sidebar {
    width: 220px;
    background: linear-gradient(180deg, hsl(220, 13%, 18%) 0%, hsl(220, 13%, 15%) 100%);
    border-right: 1px solid hsl(217, 91%, 60%, 0.1);
    padding: 1.5rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    color: var(--muted-foreground);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar-item::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-item.active {
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    box-shadow: 0 4px 12px hsl(217, 91%, 60%, 0.3);
}

.sidebar-item:not(.active):hover {
    background: hsl(220, 13%, 25%);
    color: var(--foreground);
}

.sidebar-item:not(.active):hover::before {
    opacity: 1;
}

.item-icon {
    font-size: 1.125rem;
    width: 24px;
    text-align: center;
}

.mockup-main {
    flex: 1;
    padding: 2rem;
    background: linear-gradient(135deg, hsl(220, 13%, 22%) 0%, hsl(220, 13%, 18%) 100%);
}

.chart-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.1);
}

.chart-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.chart-title {
    font-weight: 700;
    color: var(--foreground);
    font-size: 1.125rem;
}

.chart-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--foreground);
}

.chart-change {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.chart-change.positive {
    background: hsl(var(--success), 0.15);
    color: var(--success);
    border: 1px solid hsl(var(--success), 0.3);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    background: transparent;
    color: var(--muted-foreground);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn.active {
    background: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
}

.chart-btn:not(.active):hover {
    background: var(--card);
    color: var(--foreground);
}

.chart-area {
    flex: 1;
    position: relative;
    margin-bottom: 1rem;
}

.trading-chart {
    width: 100%;
    height: 100%;
}

.chart-line {
    filter: drop-shadow(0 0 12px hsl(217, 91%, 60%, 0.6));
}

.chart-point {
    filter: drop-shadow(0 0 8px hsl(217, 91%, 60%, 0.8));
    animation: pulse 2s infinite;
}

.chart-metrics {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: hsl(220, 13%, 20%);
    border-radius: 0.75rem;
    border: 1px solid hsl(217, 91%, 60%, 0.1);
}

/* General metric styles removed to avoid conflicts with analytics-specific styles */

/* Enhanced Features Section */
.features-section {
    padding: 8rem 0;
    position: relative;
    background: linear-gradient(135deg, hsl(220, 13%, 8%) 0%, hsl(220, 13%, 5%) 50%, hsl(220, 13%, 8%) 100%);
}

.features-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
        radial-gradient(circle at 25% 25%, hsl(217, 91%, 60%, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, hsl(178, 60%, 48%, 0.06) 0%, transparent 50%);
    pointer-events: none;
}

.section-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-glass);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
    padding: 0.5rem 1.25rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 2rem;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
}

.badge-icon {
    font-size: 1rem;
}

.section-title {
    margin-bottom: 1.5rem;
}

.title-main {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
}

.title-sub {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--muted-foreground);
}

.section-description {
    font-size: 1.25rem;
    color: var(--muted-foreground);
    line-height: 1.7;
    max-width: 700px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-bottom: 4rem;
    align-items: stretch;
}

.feature-card {
    background: var(--gradient-glass);
    border: 1px solid var(--border);
    border-radius: 1.5rem;
    padding: 2.5rem;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: var(--shadow-glass);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.feature-card::before {
    content: '';
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
    border-radius: inherit;
}

.feature-card.primary::before {
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.08) 0%, transparent 50%, hsl(217, 91%, 60%, 0.04) 100%);
}

.feature-card.secondary::before {
    background: linear-gradient(135deg, hsl(178, 60%, 48%, 0.08) 0%, transparent 50%, hsl(178, 60%, 48%, 0.04) 100%);
}

.feature-card.tertiary::before {
    background: linear-gradient(135deg, hsl(262, 83%, 58%, 0.08) 0%, transparent 50%, hsl(262, 83%, 58%, 0.04) 100%);
}

.feature-card:hover {
    transform: translateY(-12px);
    box-shadow:
        0 25px 50px hsl(0, 0%, 0%, 0.3),
        0 12px 25px hsl(217, 91%, 60%, 0.15);
}

.feature-card.primary:hover {
    border-color: hsl(217, 91%, 60%, 0.4);
}

.feature-card.secondary:hover {
    border-color: hsl(178, 60%, 48%, 0.4);
}

.feature-card.tertiary:hover {
    border-color: hsl(262, 83%, 58%, 0.4);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.feature-icon {
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1.25rem;
    position: relative;
    overflow: hidden;
}

.feature-card.primary .feature-icon {
    background: var(--gradient-primary);
    color: var(--primary-foreground);
}

.feature-card.secondary .feature-icon {
    background: linear-gradient(135deg, hsl(178, 60%, 48%) 0%, hsl(178, 60%, 58%) 100%);
    color: var(--primary-foreground);
}

.feature-card.tertiary .feature-icon {
    background: linear-gradient(135deg, hsl(262, 83%, 58%) 0%, hsl(262, 83%, 68%) 100%);
    color: var(--primary-foreground);
}

.feature-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-card.primary .feature-badge {
    background: hsl(217, 91%, 60%, 0.15);
    color: hsl(217, 91%, 60%);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
}

.feature-card.secondary .feature-badge {
    background: hsl(178, 60%, 48%, 0.15);
    color: hsl(178, 60%, 48%);
    border: 1px solid hsl(178, 60%, 48%, 0.3);
}

.feature-card.tertiary .feature-badge {
    background: hsl(262, 83%, 58%, 0.15);
    color: hsl(262, 83%, 58%);
    border: 1px solid hsl(262, 83%, 58%, 0.3);
}

.feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.feature-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--foreground);
}

.feature-description {
    color: var(--muted-foreground);
    line-height: 1.7;
    margin-bottom: 2rem;
    flex: 1;
}

.feature-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: hsl(220, 13%, 20%, 0.5);
    border-radius: 1rem;
    border: 1px solid hsl(220, 13%, 30%, 0.3);
}

.stat {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--foreground);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

.highlight-item svg {
    color: var(--success);
    flex-shrink: 0;
}

/* Features CTA */
.features-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3rem;
    background: var(--gradient-glass);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
    border-radius: 1.5rem;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.features-cta::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.05) 0%, transparent 50%, hsl(178, 60%, 48%, 0.05) 100%);
    pointer-events: none;
}

.cta-content {
    flex: 1;
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: 0.5rem;
}

.cta-description {
    font-size: 1.125rem;
    color: var(--muted-foreground);
}

.cta-button {
    padding: 1.25rem 2.5rem;
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1.125rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 2;
    box-shadow: 0 12px 32px hsl(217, 91%, 60%, 0.4);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 16px 40px hsl(217, 91%, 60%, 0.5);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        gap: 4rem;
    }

    .stats-grid {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .stat-item {
        max-width: 400px;
        width: 100%;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .features-cta {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .dashboard-mockup {
        max-width: 500px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-section {
        padding: 6rem 0 4rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.25rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .hero-trust-indicators {
        justify-content: center;
        flex-wrap: wrap;
    }

    .dashboard-mockup {
        transform: none;
        max-width: 100%;
    }

    .mockup-content {
        height: 350px;
    }

    .mockup-sidebar {
        width: 180px;
        padding: 1rem 0.75rem;
    }

    .sidebar-item {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .item-icon {
        font-size: 1rem;
    }

    .mockup-main {
        padding: 1.5rem;
    }

    .chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .chart-info {
        gap: 1rem;
    }

    .chart-price {
        font-size: 1.25rem;
    }

    .feature-card {
        padding: 2rem;
    }

    .feature-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 1rem;
    }

    .hero-section {
        padding: 5rem 0 3rem;
    }

    .hero-container {
        padding: 0 1rem;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .title-main {
        font-size: 2rem;
    }

    .title-sub {
        font-size: 1.125rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .btn-hero-primary {
        width: 100%;
        justify-content: center;
        padding: 1rem 2rem;
        font-size: 1rem;
        max-width: 300px;
    }

    .section-container {
        padding: 0 1rem;
    }

    .section-title .title-main {
        font-size: 2rem;
    }

    .section-title .title-sub {
        font-size: 1rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
    }

    .feature-title {
        font-size: 1.5rem;
    }

    .feature-stats {
        padding: 1rem;
    }

    .features-cta {
        padding: 2rem;
        text-align: center;
    }

    .cta-title {
        font-size: 1.5rem;
    }

    .cta-description {
        font-size: 1rem;
    }

    .cta-button {
        width: 100%;
        justify-content: center;
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .dashboard-mockup {
        border-radius: 1rem;
    }

    .mockup-content {
        height: 300px;
        flex-direction: column;
    }

    .mockup-sidebar {
        width: 100%;
        height: 80px;
        border-right: none;
        border-bottom: 1px solid hsl(217, 91%, 60%, 0.1);
        padding: 0.75rem;
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        overflow-x: auto;
    }

    .sidebar-item {
        white-space: nowrap;
        margin-bottom: 0;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        flex-shrink: 0;
    }

    .item-icon {
        display: none;
    }

    .mockup-main {
        padding: 1rem;
    }

    .chart-metrics {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Metric item styling moved to analytics-specific section */
}

/* Analytics Section */
.analytics-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.02) 0%, hsl(178, 60%, 48%, 0.02) 100%);
}

.analytics-grid {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 1.5rem;
    min-height: 400px;
    align-items: stretch;
    max-width: 1200px;
    margin: 0 auto;
}

.analytics-card {
    background: var(--gradient-glass);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    box-shadow: var(--shadow-glass);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    height: auto;
}

.analytics-card.large {
    grid-row: 1 / 3;
    grid-column: 1 / 2;
}

.analytics-card:nth-child(2) {
    grid-row: 1 / 2;
    grid-column: 2 / 4;
}

.analytics-card:nth-child(3) {
    grid-row: 2 / 3;
    grid-column: 2 / 4;
}

.analytics-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
    border-color: hsl(217, 91%, 60%, 0.3);
}

.analytics-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border);
}

.analytics-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
    letter-spacing: -0.025em;
}

.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 0;
}

.analytics-card .performance-badge {
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    font-size: 0.875rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

.analytics-card .performance-badge.positive {
    background: hsl(160, 84%, 39%, 0.2);
    color: var(--success);
    border: 1px solid hsl(160, 84%, 39%, 0.4);
    box-shadow: 0 0 12px hsl(160, 84%, 39%, 0.2);
}

.analytics-card .performance-badge.negative {
    background: hsl(0, 84%, 60%, 0.2);
    color: var(--destructive);
    border: 1px solid hsl(0, 84%, 60%, 0.4);
    box-shadow: 0 0 12px hsl(0, 84%, 60%, 0.2);
}

.status-indicator {
    padding: 0.375rem 0.875rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.status-indicator.active {
    background: hsl(160, 84%, 39%, 0.15);
    color: var(--success);
    border: 1px solid hsl(160, 84%, 39%, 0.3);
}

.signal-strength {
    padding: 0.375rem 0.875rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.signal-strength.strong {
    background: hsl(217, 91%, 60%, 0.15);
    color: var(--primary);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
}

.analytics-card .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    min-height: 200px;
    position: relative;
    background: linear-gradient(135deg,
        hsl(220, 13%, 15%) 0%,
        hsl(220, 13%, 18%) 50%,
        hsl(220, 13%, 12%) 100%);
    border-radius: 1rem;
    border: 1px solid var(--border);
    padding: 1.25rem;
    overflow: hidden;
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.2);
}

.analytics-card .chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 30% 20%,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 50%
    );
    pointer-events: none;
}

.analytics-card .portfolio-chart {
    width: 100%;
    height: 100%;
    border-radius: 0.75rem;
    flex-shrink: 0;
    background: transparent;
    border: none;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.analytics-card .portfolio-chart:hover {
    transform: scale(1.02);
}

/* Chart tooltip styling */
.chart-tooltip {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border-radius: 8px !important;
    backdrop-filter: blur(16px) !important;
    background: rgba(15, 23, 42, 0.95) !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
}



/* Enhanced chart container for smaller format */
.analytics-card:not(.large) .chart-container {
    min-height: 140px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.analytics-card:not(.large) .portfolio-chart {
    height: 120px;
}



/* Analytics Card Metrics Styling */
.analytics-card .metrics-row {
    display: flex;
    gap: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
    flex-shrink: 0;
    margin-top: auto;
}

.analytics-card .metric-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    text-align: left;
}

.analytics-card .metric-label {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.analytics-card .metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    line-height: 1.2;
}

.analytics-card .metric-value.positive {
    color: var(--success);
}

.analytics-card .metric-value.negative {
    color: var(--destructive);
}

.bot-list, .signals-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    justify-content: flex-start;
}

.bot-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    background: hsl(220, 13%, 25%, 0.5);
    border: 1px solid hsl(220, 13%, 30%, 0.5);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    min-height: 40px;
    font-size: 0.875rem;
}

.bot-item:hover {
    background: hsl(220, 13%, 25%, 0.8);
    border-color: hsl(217, 91%, 60%, 0.3);
    transform: translateY(-2px);
}

.bot-name {
    font-weight: 600;
    color: var(--foreground);
    font-size: 0.875rem;
}

.bot-profit {
    font-size: 0.875rem;
    font-weight: 600;
}

.bot-profit.positive {
    color: var(--success);
}

.bot-profit.negative {
    color: var(--destructive);
}

.bot-stats {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Enhanced styling for Active Bots card when in large format */
.analytics-card.large .bot-list {
    margin-bottom: 1rem;
    max-height: none;
}

.analytics-card.large .bot-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
}

.analytics-card.large .bot-stats {
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border);
}

.analytics-card.large .bot-stats .stat-item {
    text-align: center;
}

.analytics-card.large .bot-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
}

.analytics-card.large .bot-stats .stat-label {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Adjust Portfolio Performance card for smaller format */
.analytics-card:not(.large) .chart-container {
    min-height: 120px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.analytics-card:not(.large) .portfolio-chart {
    height: 100px;
}

.analytics-card:not(.large) .metrics-row {
    gap: 1rem;
    padding-top: 1rem;
}

.analytics-card:not(.large) .metric-value {
    font-size: 1.25rem;
}

.analytics-card:not(.large) .performance-badge {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--foreground);
}

.signal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: hsl(220, 13%, 25%, 0.5);
    border: 1px solid hsl(220, 13%, 30%, 0.5);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    min-height: 60px;
    font-size: 0.875rem;
    height: 100%;
}

.signal-item:hover {
    background: hsl(220, 13%, 25%, 0.8);
    border-color: hsl(217, 91%, 60%, 0.3);
    transform: translateY(-2px);
}

.signal-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.signal-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.signal-pair {
    font-weight: 600;
    color: var(--foreground);
    font-size: 0.875rem;
}

.signal-type {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

.bot-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.bot-status.running {
    background: var(--success);
    box-shadow: 0 0 8px hsl(var(--success), 0.5);
}

.signal-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    flex-shrink: 0;
}

.signal-icon.buy {
    background: hsl(160, 84%, 39%, 0.15);
    color: var(--success);
    border: 1px solid hsl(160, 84%, 39%, 0.3);
}

.signal-icon.sell {
    background: hsl(0, 84%, 60%, 0.15);
    color: var(--destructive);
    border: 1px solid hsl(0, 84%, 60%, 0.3);
}

.signal-icon.hold {
    background: hsl(32, 95%, 44%, 0.15);
    color: var(--warning);
    border: 1px solid hsl(32, 95%, 44%, 0.3);
}

.signal-confidence {
    font-weight: 700;
    color: var(--primary);
    font-size: 0.875rem;
}

.signals-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    align-items: stretch;
}

.signals-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Pricing Section */
.pricing-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, hsl(262, 83%, 58%, 0.02) 0%, hsl(217, 91%, 60%, 0.02) 100%);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    align-items: start;
    padding: 0 1rem;
}

.pricing-card {
    background: var(--gradient-glass);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 2rem;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    box-shadow: var(--shadow-glass);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
    display: flex;
    flex-direction: column;
    min-height: 600px;
    height: 100%;
}

.pricing-card.featured {
    border-color: hsl(217, 91%, 60%, 0.5);
    box-shadow: var(--shadow-glow-primary);
    transform: scale(1.02);
    z-index: 2;
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
    border-color: hsl(217, 91%, 60%, 0.3);
}

.pricing-card.featured:hover {
    transform: scale(1.02) translateY(-4px);
}

/* Adjust featured card header to account for badge */
.pricing-card.featured .plan-header {
    padding-top: 1rem;
    min-height: 160px;
}

.plan-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    padding: 0.5rem 1.5rem;
    border-radius: 0 0 1rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    z-index: 3;
}

.plan-header {
    margin-bottom: 2rem;
    flex-shrink: 0;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: 1rem;
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--foreground);
}

.price-period {
    font-size: 1rem;
    color: var(--muted-foreground);
}

.plan-description {
    color: var(--muted-foreground);
    line-height: 1.6;
}

.plan-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    text-align: left;
    flex: 1;
    min-height: 240px;
    justify-content: flex-start;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-height: 32px;
    padding: 0.25rem 0;
}

.feature-check {
    color: var(--success);
    flex-shrink: 0;
}

.plan-button {
    width: 100%;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    color: var(--foreground);
    border: 1px solid var(--border);
    margin-top: auto;
    flex-shrink: 0;
}

.plan-button.primary {
    background: var(--gradient-primary);
    color: var(--primary-foreground);
    border-color: transparent;
    box-shadow: 0 8px 24px hsl(217, 91%, 60%, 0.3);
}

.plan-button:hover {
    background: var(--card);
    border-color: var(--primary);
    transform: translateY(-2px);
}

.plan-button.primary:hover {
    background: var(--gradient-primary);
    box-shadow: 0 12px 32px hsl(217, 91%, 60%, 0.4);
    transform: translateY(-2px);
}

/* Footer Section */
.footer-section {
    padding: 4rem 0 2rem;
    background: linear-gradient(135deg, hsl(220, 13%, 15%) 0%, hsl(220, 13%, 10%) 100%);
    border-top: 1px solid var(--border);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand {
    max-width: 400px;
}

.footer-description {
    color: var(--muted-foreground);
    line-height: 1.6;
    margin-top: 1rem;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.link-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.link-title {
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.5rem;
}

.footer-link {
    color: var(--muted-foreground);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--foreground);
}

.footer-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 2rem;
    border-top: 1px solid var(--border);
}

.copyright {
    color: var(--muted-foreground);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    color: var(--muted-foreground);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
    transform: translateY(-2px);
}

/* Additional Responsive Styles */
@media (max-width: 1200px) {
    .analytics-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto auto;
        max-width: 800px;
    }

    .analytics-card.large {
        grid-row: 1 / 2;
        grid-column: 1 / 3;
    }

    .analytics-card:nth-child(2) {
        grid-row: 2 / 3;
        grid-column: 1 / 2;
    }

    .analytics-card:nth-child(3) {
        grid-row: 2 / 3;
        grid-column: 2 / 3;
    }

    /* Adjust Active Bots card for medium screens when large */
    .analytics-card.large .bot-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }

    .analytics-card.large .bot-stats {
        grid-column: 1 / 3;
        margin-top: 1rem;
    }
}

@media (max-width: 1024px) {
    .analytics-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        min-height: auto;
        gap: 1rem;
    }

    .analytics-card.large {
        grid-row: auto;
        grid-column: auto;
    }

    .analytics-card:nth-child(2),
    .analytics-card:nth-child(3) {
        grid-row: auto;
        grid-column: auto;
    }

    .analytics-card {
        min-height: 250px;
        height: auto;
    }

    .analytics-card .metrics-row {
        gap: 1rem;
        padding-top: 1rem;
    }

    .analytics-card .metric-value {
        font-size: 1.25rem;
    }

    .analytics-card .chart-container {
        min-height: 160px;
        padding: 1rem;
        border-radius: 0.75rem;
    }

    .analytics-card .portfolio-chart {
        height: 140px;
    }

    .chart-tooltip {
        font-size: 11px !important;
        padding: 6px 10px !important;
    }



    .analytics-card .performance-badge {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Mobile adjustments for swapped layout */
    .analytics-card.large .bot-list {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .analytics-card.large .bot-stats {
        grid-column: 1;
        grid-template-columns: 1fr;
        gap: 0.75rem;
        text-align: center;
    }

    .analytics-card.large .bot-stats .stat-value {
        font-size: 1.25rem;
    }

    .signals-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1024px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 600px;
    }

    .pricing-card {
        min-height: 500px;
    }

    .pricing-card.featured {
        transform: none;
        order: -1;
    }

    .pricing-card.featured:hover {
        transform: translateY(-4px);
    }

    .plan-header {
        min-height: 120px;
    }

    .pricing-card.featured .plan-header {
        min-height: 140px;
    }

    .plan-features {
        min-height: 200px;
    }
}

@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .pricing-card {
        min-height: 450px;
        padding: 1.5rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-4px);
    }

    .plan-header {
        min-height: 100px;
        margin-bottom: 1.5rem;
    }

    .pricing-card.featured .plan-header {
        min-height: 120px;
        padding-top: 0.75rem;
    }

    .plan-features {
        min-height: 180px;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .plan-button {
        padding: 0.875rem 1.5rem;
    }

    .footer-links {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* React Component Specific Overrides */
.landing-page * {
    box-sizing: border-box;
}

.landing-page h1, .landing-page h2, .landing-page h3, .landing-page h4, .landing-page h5, .landing-page h6 {
    margin: 0;
}

.landing-page p {
    margin: 0;
}

.landing-page button {
    font-family: inherit;
}

/* Ensure proper font rendering */
.landing-page {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Animation classes for React components */
.feature-card, .analytics-card, .pricing-card, .stat-item {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card.animate-in, .analytics-card.animate-in, .pricing-card.animate-in,
.stat-item.animate-in {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Initial animation state for scroll-triggered animations */
.feature-card:not(.animate-in), .analytics-card:not(.animate-in),
.pricing-card:not(.animate-in), .stat-item:not(.animate-in) {
    opacity: 0.8;
    transform: translateY(20px) scale(0.98);
}

/* Enhanced hover effects for React components */
.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.dashboard-mockup:hover .chart-point {
    animation: pulse 1s infinite;
}

.btn-hero-primary:hover, .btn-hero-secondary:hover {
    animation: buttonPulse 0.6s ease;
}

@keyframes buttonPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Ensure proper performance for animations */
.feature-card, .analytics-card, .pricing-card, .dashboard-mockup,
.btn-hero-primary, .btn-hero-secondary, .stat-item {
    will-change: transform;
}

/* Premium Dashboard Mockup Styles */
.premium-dashboard-mockup {
    background: linear-gradient(135deg,
        hsl(220, 13%, 18%) 0%,
        hsl(220, 13%, 15%) 50%,
        hsl(220, 13%, 12%) 100%);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
    border-radius: 1rem;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 20px 40px hsl(217, 91%, 60%, 0.15),
        0 8px 20px hsl(0, 0%, 0%, 0.4),
        inset 0 1px 0 hsl(255, 255%, 255%, 0.1);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-6deg) rotateX(3deg);
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    max-width: 600px;
    width: 100%;
    height: 400px;
}

.mockup-glow-premium {
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg,
        hsl(217, 91%, 60%, 0.4),
        hsl(178, 60%, 48%, 0.3),
        hsl(262, 83%, 58%, 0.4));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.premium-dashboard-mockup:hover {
    transform: perspective(1000px) rotateY(-3deg) rotateX(1deg) scale(1.01);
    box-shadow:
        0 25px 50px hsl(217, 91%, 60%, 0.25),
        0 10px 25px hsl(0, 0%, 0%, 0.5),
        inset 0 1px 0 hsl(255, 255%, 255%, 0.15);
}

.premium-dashboard-mockup:hover .mockup-glow-premium {
    opacity: 1;
}

/* Browser Header */
.mockup-browser-header {
    background: linear-gradient(135deg, hsl(220, 13%, 22%) 0%, hsl(220, 13%, 18%) 100%);
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.2);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    height: 50px;
}

.browser-controls {
    display: flex;
    gap: 0.5rem;
}

.browser-url {
    flex: 1;
    max-width: 400px;
}

.url-bar {
    background: hsl(220, 13%, 25%);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.url-protocol {
    color: hsl(210, 40%, 70%);
}

.url-domain {
    color: hsl(210, 40%, 98%);
    font-weight: 500;
}

.ssl-indicator {
    margin-left: auto;
    font-size: 0.75rem;
}

.browser-actions {
    display: flex;
    align-items: center;
}

.live-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: hsl(142, 76%, 36%, 0.2);
    border: 1px solid hsl(142, 76%, 36%, 0.3);
    border-radius: 0.5rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: hsl(142, 76%, 56%);
}

.live-pulse {
    width: 6px;
    height: 6px;
    background: hsl(142, 76%, 56%);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Dashboard Content */
.premium-dashboard-content {
    height: 350px;
    background: linear-gradient(135deg,
        hsl(220, 13%, 15%) 0%,
        hsl(220, 13%, 12%) 100%);
    display: flex;
    flex-direction: column;
}

/* Top Navigation Bar */
.dashboard-topbar {
    background: linear-gradient(135deg, hsl(220, 13%, 18%) 0%, hsl(220, 13%, 15%) 100%);
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.1);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(178, 60%, 48%) 100%);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(178, 60%, 48%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-tabs {
    display: flex;
    gap: 0.5rem;
}

.nav-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: hsl(210, 40%, 70%);
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-tab.active {
    background: linear-gradient(135deg, hsl(217, 91%, 60%, 0.2) 0%, hsl(178, 60%, 48%, 0.1) 100%);
    color: hsl(217, 91%, 60%);
    border: 1px solid hsl(217, 91%, 60%, 0.3);
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.portfolio-summary {
    text-align: right;
}

.portfolio-value {
    display: block;
    font-size: 1.125rem;
    font-weight: 700;
    color: hsl(210, 40%, 98%);
}

.portfolio-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.portfolio-change.positive {
    color: hsl(142, 76%, 56%);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(178, 60%, 48%) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
}

/* Dashboard Grid */
.dashboard-grid {
    flex: 1;
    padding: 1rem;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    gap: 0.75rem;
    overflow: hidden;
}

/* Market Cards */
.market-cards {
    grid-column: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.market-card {
    background: linear-gradient(135deg, hsl(220, 13%, 20%) 0%, hsl(220, 13%, 16%) 100%);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 0.75rem;
    padding: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.market-card:hover {
    transform: translateY(-2px);
    border-color: hsl(217, 91%, 60%, 0.4);
    box-shadow: 0 8px 24px hsl(217, 91%, 60%, 0.15);
}

.market-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.crypto-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.crypto-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.market-card.btc .crypto-icon {
    background: linear-gradient(135deg, #f7931a 0%, #ff9500 100%);
    color: white;
}

.market-card.eth .crypto-icon {
    background: linear-gradient(135deg, #627eea 0%, #4f46e5 100%);
    color: white;
}

.market-card.sol .crypto-icon {
    background: linear-gradient(135deg, #9945ff 0%, #8b5cf6 100%);
    color: white;
}

.crypto-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.crypto-symbol {
    font-weight: 600;
    color: hsl(210, 40%, 98%);
    font-size: 0.875rem;
}

.crypto-name {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

.price-info {
    text-align: right;
}

.current-price {
    display: block;
    font-size: 1.125rem;
    font-weight: 700;
    color: hsl(210, 40%, 98%);
    margin-bottom: 0.25rem;
}

.price-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.price-change.positive {
    background: hsl(142, 76%, 36%, 0.2);
    color: hsl(142, 76%, 56%);
}

.price-change.negative {
    background: hsl(0, 84%, 60%, 0.2);
    color: hsl(0, 84%, 60%);
}

.mini-chart {
    height: 40px;
    margin-top: 0.5rem;
}

.price-chart {
    width: 100%;
    height: 100%;
}

/* Trading Bots Panel */
.trading-bots-panel {
    background: linear-gradient(135deg, hsl(220, 13%, 20%) 0%, hsl(220, 13%, 16%) 100%);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 1rem;
    padding: 1rem;
    display: none;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.1);
}

.panel-title {
    font-size: 1rem;
    font-weight: 600;
    color: hsl(210, 40%, 98%);
    margin: 0;
}

.bots-count {
    font-size: 0.75rem;
    font-weight: 600;
    color: hsl(142, 76%, 56%);
    background: hsl(142, 76%, 36%, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.bots-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
}

.bot-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: hsl(220, 13%, 25%, 0.5);
    border: 1px solid hsl(220, 13%, 30%, 0.5);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.bot-item:hover {
    background: hsl(220, 13%, 25%, 0.8);
    border-color: hsl(217, 91%, 60%, 0.3);
    transform: translateY(-1px);
}

.bot-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.bot-status.running {
    background: hsl(142, 76%, 56%);
    box-shadow: 0 0 8px hsl(142, 76%, 56%, 0.5);
    animation: pulse 2s infinite;
}

.bot-status.paused {
    background: hsl(32, 95%, 44%);
}

.bot-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.bot-name {
    font-weight: 600;
    color: hsl(210, 40%, 98%);
    font-size: 0.875rem;
}

.bot-pair {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

.bot-performance {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.bot-profit {
    font-size: 0.875rem;
    font-weight: 600;
}

.bot-profit.positive {
    color: hsl(142, 76%, 56%);
}

.bot-profit.negative {
    color: hsl(0, 84%, 60%);
}

.bot-trades {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

/* Main Chart Container */
.main-chart-container {
    grid-column: 1;
    background: linear-gradient(135deg, hsl(220, 13%, 20%) 0%, hsl(220, 13%, 16%) 100%);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 0.75rem;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 180px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid hsl(217, 91%, 60%, 0.1);
}

.chart-title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: hsl(210, 40%, 98%);
    margin: 0;
}

.chart-price-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chart-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: hsl(210, 40%, 98%);
}

.chart-change {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.chart-change.positive {
    background: hsl(142, 76%, 36%, 0.2);
    color: hsl(142, 76%, 56%);
}

.chart-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-end;
}

.timeframe-buttons {
    display: flex;
    gap: 0.25rem;
    background: hsl(220, 13%, 25%);
    border-radius: 0.5rem;
    padding: 0.25rem;
}

.timeframe-btn {
    padding: 0.375rem 0.75rem;
    border: none;
    background: transparent;
    color: hsl(210, 40%, 70%);
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeframe-btn.active {
    background: hsl(217, 91%, 60%);
    color: white;
}

.chart-tools {
    display: flex;
    gap: 0.25rem;
}

.tool-btn {
    width: 32px;
    height: 32px;
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    background: hsl(220, 13%, 25%);
    color: hsl(210, 40%, 70%);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.tool-btn:hover {
    border-color: hsl(217, 91%, 60%, 0.4);
    background: hsl(220, 13%, 30%);
}

.advanced-chart {
    flex: 1;
    min-height: 100px;
    height: 100px;
    position: relative;
}

.trading-chart-advanced {
    width: 100%;
    height: 100%;
}

.chart-grid line {
    stroke: hsl(210, 40%, 70%);
    stroke-width: 0.5;
}

.price-indicator {
    filter: drop-shadow(0 0 4px hsl(217, 91%, 60%));
}

.chart-metrics-advanced {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid hsl(217, 91%, 60%, 0.1);
}

.metric-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.metric-group .metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-group .metric-label {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

.metric-group .metric-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: hsl(210, 40%, 98%);
}

/* Portfolio Summary Panel */
.portfolio-summary-panel {
    background: linear-gradient(135deg, hsl(220, 13%, 20%) 0%, hsl(220, 13%, 16%) 100%);
    border: 1px solid hsl(217, 91%, 60%, 0.2);
    border-radius: 1rem;
    padding: 1rem;
    display: none;
    flex-direction: column;
}

.portfolio-total {
    font-size: 1.125rem;
    font-weight: 700;
    color: hsl(142, 76%, 56%);
}

.portfolio-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
}

.asset-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: hsl(220, 13%, 25%, 0.5);
    border: 1px solid hsl(220, 13%, 30%, 0.5);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.asset-item:hover {
    background: hsl(220, 13%, 25%, 0.8);
    border-color: hsl(217, 91%, 60%, 0.3);
    transform: translateY(-1px);
}

.asset-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.asset-symbol {
    font-weight: 600;
    color: hsl(210, 40%, 98%);
    font-size: 0.875rem;
}

.asset-amount {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

.asset-value {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.asset-usd {
    font-size: 0.875rem;
    font-weight: 600;
    color: hsl(210, 40%, 98%);
}

.asset-percentage {
    font-size: 0.75rem;
    color: hsl(210, 40%, 70%);
}

/* Responsive Design for Premium Dashboard */
@media (max-width: 1024px) {
    .premium-dashboard-mockup {
        max-width: 700px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }

    .market-cards {
        grid-column: 1;
        grid-template-columns: 1fr;
    }

    .main-chart-container {
        grid-column: 1;
        order: 1;
    }

    .trading-bots-panel,
    .portfolio-summary-panel {
        grid-column: 1;
    }
}

@media (max-width: 768px) {
    .premium-dashboard-mockup {
        max-width: 500px;
        transform: perspective(800px) rotateY(-4deg) rotateX(2deg);
    }

    .premium-dashboard-content {
        height: 400px;
    }

    .dashboard-topbar {
        padding: 0.75rem 1rem;
    }

    .topbar-left {
        gap: 1rem;
    }

    .nav-tabs {
        display: none;
    }

    .portfolio-summary {
        display: none;
    }

    .dashboard-grid {
        padding: 1rem;
        gap: 0.75rem;
    }

    .trading-bots-panel,
    .portfolio-summary-panel {
        display: none;
    }

    .chart-controls {
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }

    .chart-tools {
        display: none;
    }
}
