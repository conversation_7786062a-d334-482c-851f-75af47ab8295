# 🚀 CI/CD Pipeline Setup

Your project is now configured with a complete CI/CD pipeline that automatically deploys to Vercel!

## ✨ What's New

- **Automatic Deployments**: Push to `main` or `cicd-fe` branches triggers production deployment
- **Preview Deployments**: Pull requests get preview deployments automatically
- **Quality Gates**: Code quality, testing, and type checking before deployment
- **Local Deployment**: Scripts for manual deployment when needed

## 🔑 Required Setup

### 1. GitHub Secrets

Add this secret to your GitHub repository:

1. Go to **Settings** → **Secrets and variables** → **Actions**
2. Click **New repository secret**
3. Name: `VERCEL_TOKEN`
4. Value: `************************`

### 2. Vercel Project Configuration

Update `.vercel/project.json` with your actual project details:

```json
{
  "projectId": "your-actual-project-id",
  "orgId": "your-actual-org-id",
  "settings": {
    "framework": "vite",
    "buildCommand": "npm run build",
    "outputDirectory": "dist",
    "installCommand": "npm ci"
  }
}
```

## 🎯 How It Works

### Branch Strategy

- **`main`** → Production deployment
- **`cicd-fe`** → Production deployment  
- **`develop`** → Quality checks only
- **Pull Requests** → Preview deployment

### Pipeline Stages

1. **Quality Checks** (runs on all branches)
   - Type checking
   - Linting
   - Testing
   - Coverage reports

2. **Build** (runs after quality checks pass)
   - Production build
   - Artifact storage

3. **Deploy** (runs on main/cicd-fe branches)
   - Automatic Vercel deployment
   - Production environment

## 🚀 Quick Start

1. **Push to main branch**:
   ```bash
   git add .
   git commit -m "feat: enable CI/CD pipeline"
   git push origin main
   ```

2. **Monitor deployment**:
   - Check GitHub Actions tab
   - Check Vercel dashboard
   - Your site will be live in minutes!

## 🔧 Manual Deployment

```bash
# Set your token
export VERCEL_TOKEN=************************

# Deploy preview
./scripts/deploy-vercel.sh

# Deploy production
./scripts/deploy-vercel.sh --prod
```

## 📊 Monitoring

- **GitHub Actions**: View pipeline status and logs
- **Vercel Dashboard**: Monitor deployments and performance
- **Coverage Reports**: Code coverage uploaded to Codecov

## 🚨 Troubleshooting

### Common Issues

1. **Build fails**: Check GitHub Actions logs for errors
2. **Deployment fails**: Verify Vercel token and project configuration
3. **Tests fail**: Fix failing tests before deployment

### Debug Commands

```bash
# Run tests locally
npm run test:run

# Check types
npm run type-check

# Lint code
npm run lint

# Build locally
npm run build
```

## 🎉 Success!

Once configured, every push to your main branch will automatically:
- ✅ Run quality checks
- ✅ Build your application
- ✅ Deploy to Vercel
- ✅ Make your changes live!

Your CI/CD pipeline is ready to go! 🚀
