<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kamikaze Trading Pro - Advanced AI Crypto Trading Platform</title>
    <meta name="description" content="Revolutionary AI-powered cryptocurrency trading platform with automated bots, real-time analytics, and advanced risk management. Join thousands of successful traders.">
    <meta name="keywords" content="crypto trading, AI trading bots, cryptocurrency, automated trading, trading platform, blockchain">
    <meta name="author" content="Kamikaze Trading Pro">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Kamikaze Trading Pro - Advanced AI Crypto Trading Platform">
    <meta property="og:description" content="Revolutionary AI-powered cryptocurrency trading platform with automated bots, real-time analytics, and advanced risk management.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kamikaze-trading.com">
    <meta property="og:image" content="https://kamikaze-trading.com/assets/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Kamikaze Trading Pro - Advanced AI Crypto Trading Platform">
    <meta name="twitter:description" content="Revolutionary AI-powered cryptocurrency trading platform with automated bots, real-time analytics, and advanced risk management.">
    <meta name="twitter:image" content="https://kamikaze-trading.com/assets/twitter-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="landing.css" as="style">
    <link rel="preload" href="landing.js" as="script">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="landing.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <header class="header-nav">
        <nav class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="url(#brandGradient)" stroke="currentColor" stroke-width="1"/>
                        <defs>
                            <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3b82f6"/>
                                <stop offset="100%" style="stop-color:#14b8a6"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <span class="brand-text">Kamikaze</span>
                <span class="brand-badge">PRO</span>
            </div>
            
            <div class="nav-menu" id="navMenu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#analytics" class="nav-link">Analytics</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
            </div>
            
            <div class="nav-actions">
                <button class="btn-secondary" onclick="openApp()">Sign In</button>
                <button class="btn-primary" onclick="openApp()">Get Started</button>
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="hero-gradient-1"></div>
            <div class="hero-gradient-2"></div>
            <div class="hero-gradient-3"></div>
        </div>

        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line-1">The Future of</span>
                    <span class="title-line-2">
                        <span class="gradient-text-enhanced">AI Trading</span>
                    </span>
                    <span class="title-line-3">Starts Here</span>
                </h1>

                <p class="hero-description">
                    Experience the next generation of cryptocurrency trading with our advanced AI-powered platform.
                    Automated strategies, real-time analytics, and intelligent risk management - all in one place.
                </p>

                <div class="hero-stats-container">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">$2.4B+</div>
                                <div class="stat-label">Trading Volume</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">150K+</div>
                                <div class="stat-label">Active Traders</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">Uptime</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="hero-actions">
                    <button class="btn-hero-primary" onclick="openApp()">
                        <div class="btn-content">
                            <span>Start Trading Now</span>
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="btn-glow"></div>
                    </button>
                </div>
            </div>

            <div class="hero-visual">
                <div class="visual-background">
                    <div class="floating-element element-1"></div>
                    <div class="floating-element element-2"></div>
                    <div class="floating-element element-3"></div>
                </div>

                <div class="dashboard-mockup">
                    <div class="mockup-glow"></div>
                    <div class="mockup-header">
                        <div class="mockup-controls">
                            <span class="control-dot red"></span>
                            <span class="control-dot yellow"></span>
                            <span class="control-dot green"></span>
                        </div>
                        <div class="mockup-title">
                            <span>Kamikaze Trading Dashboard</span>
                            <div class="live-indicator">
                                <div class="live-dot"></div>
                                <span>LIVE</span>
                            </div>
                        </div>
                    </div>
                    <div class="mockup-content">
                        <div class="mockup-sidebar">
                            <div class="sidebar-item active">
                                <div class="item-icon">📊</div>
                                <span>Dashboard</span>
                            </div>
                            <div class="sidebar-item">
                                <div class="item-icon">🤖</div>
                                <span>Trading Bots</span>
                            </div>
                            <div class="sidebar-item">
                                <div class="item-icon">📈</div>
                                <span>Analytics</span>
                            </div>
                            <div class="sidebar-item">
                                <div class="item-icon">💼</div>
                                <span>Portfolio</span>
                            </div>
                        </div>
                        <div class="mockup-main">
                            <div class="chart-container">
                                <div class="chart-header">
                                    <div class="chart-info">
                                        <span class="chart-title">BTC/USDT</span>
                                        <span class="chart-price">$43,250.00</span>
                                        <span class="chart-change positive">+2.45%</span>
                                    </div>
                                    <div class="chart-controls">
                                        <button class="chart-btn active">1H</button>
                                        <button class="chart-btn">4H</button>
                                        <button class="chart-btn">1D</button>
                                    </div>
                                </div>
                                <div class="chart-area">
                                    <svg class="trading-chart" viewBox="0 0 400 200">
                                        <defs>
                                            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.4"/>
                                                <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0"/>
                                            </linearGradient>
                                            <filter id="glow">
                                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                                <feMerge>
                                                    <feMergeNode in="coloredBlur"/>
                                                    <feMergeNode in="SourceGraphic"/>
                                                </feMerge>
                                            </filter>
                                        </defs>
                                        <path class="chart-line" d="M0,150 Q100,120 200,100 T400,80" stroke="#3b82f6" stroke-width="3" fill="none" filter="url(#glow)"/>
                                        <path class="chart-fill" d="M0,150 Q100,120 200,100 T400,80 L400,200 L0,200 Z" fill="url(#chartGradient)"/>
                                        <circle class="chart-point" cx="400" cy="80" r="4" fill="#3b82f6"/>
                                    </svg>
                                </div>
                                <div class="chart-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Volume</span>
                                        <span class="metric-value">$1.2M</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">High</span>
                                        <span class="metric-value">$43,890</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Low</span>
                                        <span class="metric-value">$42,150</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="section-container">
            <div class="section-header">
                <div class="section-badge">
                    <span class="badge-icon">⚡</span>
                    <span>Core Features</span>
                </div>
                <h2 class="section-title">
                    <span class="title-main">Powerful Features</span>
                    <span class="title-sub">Built for Success</span>
                </h2>
                <p class="section-description">
                    Everything you need to dominate the crypto markets with confidence and precision.
                    Our advanced platform combines cutting-edge technology with intuitive design.
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card primary">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="currentColor" opacity="0.2"/>
                                <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="16" cy="16" r="4" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="feature-badge">AI Powered</div>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">AI Trading Bots</h3>
                        <p class="feature-description">
                            Advanced machine learning algorithms that adapt to market conditions and execute trades 24/7.
                            Our bots learn from market patterns and optimize strategies in real-time.
                        </p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-number">95%</span>
                                <span class="stat-label">Success Rate</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">24/7</span>
                                <span class="stat-label">Active Trading</span>
                            </div>
                        </div>
                        <div class="feature-highlights">
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Machine Learning Optimization</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Multi-Exchange Support</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Risk-Adjusted Strategies</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-card secondary">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <rect x="4" y="4" width="24" height="24" rx="4" fill="currentColor" opacity="0.2"/>
                                <rect x="4" y="4" width="24" height="24" rx="4" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 12h16M8 16h16M8 20h12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <circle cx="24" cy="8" r="2" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="feature-badge">Real-time</div>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">Advanced Analytics</h3>
                        <p class="feature-description">
                            Comprehensive market analysis with live charts, technical indicators, and performance metrics.
                            Make informed decisions with our professional-grade analytics suite.
                        </p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-number">50+</span>
                                <span class="stat-label">Indicators</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">1ms</span>
                                <span class="stat-label">Data Latency</span>
                            </div>
                        </div>
                        <div class="feature-highlights">
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Professional Charts</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Custom Indicators</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Market Sentiment Analysis</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-card tertiary">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <circle cx="16" cy="16" r="12" fill="currentColor" opacity="0.2"/>
                                <circle cx="16" cy="16" r="12" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 16l4 4 8-8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="16" cy="16" r="6" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                            </svg>
                        </div>
                        <div class="feature-badge">Protected</div>
                    </div>
                    <div class="feature-content">
                        <h3 class="feature-title">Risk Management</h3>
                        <p class="feature-description">
                            Intelligent stop-loss, take-profit, and portfolio diversification tools to protect your capital.
                            Advanced risk controls ensure your investments are always protected.
                        </p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-number">99.8%</span>
                                <span class="stat-label">Capital Protection</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">5+</span>
                                <span class="stat-label">Risk Models</span>
                            </div>
                        </div>
                        <div class="feature-highlights">
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Smart Stop-Loss</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Portfolio Diversification</span>
                            </div>
                            <div class="highlight-item">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M13.333 4L6 11.333 2.667 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>Real-time Monitoring</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="features-cta">
                <div class="cta-content">
                    <h3 class="cta-title">Ready to Experience the Future?</h3>
                    <p class="cta-description">Join thousands of successful traders already using our platform</p>
                </div>
                <button class="cta-button" onclick="openApp()">
                    <span>Start Your Journey</span>
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M4.167 10h11.666M10 4.167L15.833 10 10 15.833" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Analytics Section -->
    <section class="analytics-section" id="analytics">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">Real-time Analytics</h2>
                <p class="section-description">
                    Advanced market insights and performance tracking to keep you ahead of the curve.
                </p>
            </div>

            <div class="analytics-grid">
                <div class="analytics-card large">
                    <div class="card-header">
                        <h3 class="card-title">Portfolio Performance</h3>
                        <div class="performance-badge positive">+24.5%</div>
                    </div>
                    <div class="chart-container">
                        <canvas id="portfolioChart" width="400" height="200"></canvas>
                    </div>
                    <div class="metrics-row">
                        <div class="metric">
                            <span class="metric-label">Total Value</span>
                            <span class="metric-value">$125,430</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">24h Change</span>
                            <span class="metric-value positive">+$3,240</span>
                        </div>
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Active Bots</h3>
                        <span class="status-indicator active">12 Running</span>
                    </div>
                    <div class="bot-list">
                        <div class="bot-item">
                            <div class="bot-info">
                                <span class="bot-name">BTC Scalper Pro</span>
                                <span class="bot-profit positive">+12.3%</span>
                            </div>
                            <div class="bot-status running"></div>
                        </div>
                        <div class="bot-item">
                            <div class="bot-info">
                                <span class="bot-name">ETH DCA Strategy</span>
                                <span class="bot-profit positive">+8.7%</span>
                            </div>
                            <div class="bot-status running"></div>
                        </div>
                        <div class="bot-item">
                            <div class="bot-info">
                                <span class="bot-name">Altcoin Hunter</span>
                                <span class="bot-profit positive">+15.2%</span>
                            </div>
                            <div class="bot-status running"></div>
                        </div>
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3 class="card-title">Market Signals</h3>
                        <span class="signal-strength strong">Strong</span>
                    </div>
                    <div class="signals-list">
                        <div class="signal-item">
                            <div class="signal-icon buy"></div>
                            <div class="signal-info">
                                <span class="signal-pair">BTC/USDT</span>
                                <span class="signal-type">Buy Signal</span>
                            </div>
                            <span class="signal-confidence">95%</span>
                        </div>
                        <div class="signal-item">
                            <div class="signal-icon sell"></div>
                            <div class="signal-info">
                                <span class="signal-pair">ETH/USDT</span>
                                <span class="signal-type">Sell Signal</span>
                            </div>
                            <span class="signal-confidence">87%</span>
                        </div>
                        <div class="signal-item">
                            <div class="signal-icon hold"></div>
                            <div class="signal-info">
                                <span class="signal-pair">ADA/USDT</span>
                                <span class="signal-type">Hold</span>
                            </div>
                            <span class="signal-confidence">92%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section" id="pricing">
        <div class="section-container">
            <div class="section-header">
                <h2 class="section-title">Choose Your Plan</h2>
                <p class="section-description">
                    Flexible pricing options designed to scale with your trading success.
                </p>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="plan-header">
                        <h3 class="plan-name">Starter</h3>
                        <div class="plan-price">
                            <span class="price-amount">$29</span>
                            <span class="price-period">/month</span>
                        </div>
                        <p class="plan-description">Perfect for beginners getting started with automated trading.</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>3 Trading Bots</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Basic Analytics</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Email Support</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>$10K Trading Limit</span>
                        </div>
                    </div>
                    <button class="plan-button" onclick="openApp()">Get Started</button>
                </div>

                <div class="pricing-card featured">
                    <div class="plan-badge">Most Popular</div>
                    <div class="plan-header">
                        <h3 class="plan-name">Professional</h3>
                        <div class="plan-price">
                            <span class="price-amount">$99</span>
                            <span class="price-period">/month</span>
                        </div>
                        <p class="plan-description">Advanced features for serious traders and professionals.</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>15 Trading Bots</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Advanced Analytics</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Priority Support</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>$100K Trading Limit</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Custom Strategies</span>
                        </div>
                    </div>
                    <button class="plan-button primary" onclick="openApp()">Start Free Trial</button>
                </div>

                <div class="pricing-card">
                    <div class="plan-header">
                        <h3 class="plan-name">Enterprise</h3>
                        <div class="plan-price">
                            <span class="price-amount">$299</span>
                            <span class="price-period">/month</span>
                        </div>
                        <p class="plan-description">Complete solution for institutions and high-volume traders.</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Unlimited Bots</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Full Analytics Suite</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>24/7 Phone Support</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Unlimited Trading</span>
                        </div>
                        <div class="feature-item">
                            <svg class="feature-check" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M16.667 5L7.5 14.167 3.333 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>API Access</span>
                        </div>
                    </div>
                    <button class="plan-button" onclick="openApp()">Contact Sales</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section" id="contact">
        <div class="section-container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="nav-brand">
                        <div class="brand-icon">
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 2L28 8V24L16 30L4 24V8L16 2Z" fill="url(#brandGradient)" stroke="currentColor" stroke-width="1"/>
                                <defs>
                                    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6"/>
                                        <stop offset="100%" style="stop-color:#14b8a6"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <span class="brand-text">Kamikaze</span>
                        <span class="brand-badge">PRO</span>
                    </div>
                    <p class="footer-description">
                        The future of cryptocurrency trading is here. Join thousands of successful traders using our AI-powered platform.
                    </p>
                </div>

                <div class="footer-links">
                    <div class="link-group">
                        <h4 class="link-title">Product</h4>
                        <a href="#features" class="footer-link">Features</a>
                        <a href="#analytics" class="footer-link">Analytics</a>
                        <a href="#pricing" class="footer-link">Pricing</a>
                        <a href="#" class="footer-link">API</a>
                    </div>

                    <div class="link-group">
                        <h4 class="link-title">Company</h4>
                        <a href="#about" class="footer-link">About</a>
                        <a href="#" class="footer-link">Careers</a>
                        <a href="#" class="footer-link">Blog</a>
                        <a href="#contact" class="footer-link">Contact</a>
                    </div>

                    <div class="link-group">
                        <h4 class="link-title">Support</h4>
                        <a href="#" class="footer-link">Help Center</a>
                        <a href="#" class="footer-link">Documentation</a>
                        <a href="#" class="footer-link">Community</a>
                        <a href="#" class="footer-link">Status</a>
                    </div>

                    <div class="link-group">
                        <h4 class="link-title">Legal</h4>
                        <a href="#" class="footer-link">Privacy Policy</a>
                        <a href="#" class="footer-link">Terms of Service</a>
                        <a href="#" class="footer-link">Cookie Policy</a>
                        <a href="#" class="footer-link">Disclaimer</a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p class="copyright">© 2024 Kamikaze Trading Pro. All rights reserved.</p>
                <div class="social-links">
                    <a href="#" class="social-link" aria-label="Twitter">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0 0 20 3.92a8.19 8.19 0 0 1-2.357.646 4.118 4.118 0 0 0 1.804-2.27 8.224 8.224 0 0 1-2.605.996 4.107 4.107 0 0 0-6.993 3.743 11.65 11.65 0 0 1-8.457-4.287 4.106 4.106 0 0 0 1.27 5.477A4.073 4.073 0 0 1 .8 7.713v.052a4.105 4.105 0 0 0 3.292 4.022 4.095 4.095 0 0 1-1.853.07 4.108 4.108 0 0 0 3.834 2.85A8.233 8.233 0 0 1 0 16.407a11.616 11.616 0 0 0 6.29 1.84" fill="currentColor"/>
                        </svg>
                    </a>
                    <a href="#" class="social-link" aria-label="Discord">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.942 4.556a16.3 16.3 0 0 0-4.126-1.3 12.04 12.04 0 0 0-.529 1.1 15.175 15.175 0 0 0-4.573 0 11.585 11.585 0 0 0-.535-1.1 16.274 16.274 0 0 0-4.129 1.3A17.392 17.392 0 0 0 .182 13.218a15.785 15.785 0 0 0 4.963 2.521c.41-.564.773-1.16 1.084-1.785a10.63 10.63 0 0 1-1.706-.83c.143-.106.283-.217.418-.33a11.664 11.664 0 0 0 10.118 0c.137.113.277.224.418.33-.544.328-1.116.606-1.71.832a12.52 12.52 0 0 0 1.084 1.785 16.46 16.46 0 0 0 5.064-2.595 17.286 17.286 0 0 0-2.973-8.662ZM6.678 10.813a1.941 1.941 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.919 1.919 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Zm6.644 0a1.94 1.94 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.918 1.918 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Z" fill="currentColor"/>
                        </svg>
                    </a>
                    <a href="#" class="social-link" aria-label="Telegram">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0Zm4.822 6.862-1.687 7.964c-.127.565-.459.703-.93.438l-2.565-1.89-1.238 1.192c-.137.137-.252.252-.516.252l.184-2.607 4.74-4.284c.206-.184-.045-.286-.32-.102L8.732 10.73l-2.566-.802c-.558-.175-.568-.558.116-.825l10.03-3.867c.465-.175.872.102.722.825Z" fill="currentColor"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="landing.js"></script>
</body>
</html>
