#!/bin/bash

# Vercel Deployment Script
# This script can be used for both local deployment and CI/CD

set -e

echo "🚀 Starting Vercel deployment..."

# Check if VERCEL_TOKEN is set
if [ -z "$VERCEL_TOKEN" ]; then
    echo "❌ Error: VERCEL_TOKEN environment variable is not set"
    echo "Please set your Vercel token: export VERCEL_TOKEN=your_token_here"
    exit 1
fi

# Install Vercel CLI if not already installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel@latest
fi

# Build the project
echo "🔨 Building project..."
npm run build

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
if [ "$1" = "--prod" ]; then
    echo "📤 Deploying to production..."
    vercel --prod --token=$VERCEL_TOKEN --yes
else
    echo "📤 Deploying preview..."
    vercel --token=$VERCEL_TOKEN --yes
fi

echo "✅ Deployment completed successfully!"
